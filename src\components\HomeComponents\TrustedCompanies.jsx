import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import Heading from '../GlobalComponents/Heading';
import Images from '../../assets/images';

const logos = [
  Images.MakeLess,
  Images.Cowork,
  Images.Greener,
  Images.SaasToday,
  Images.Dorfus,
  Images.AsKimat,
];

const TrustedCompanies = () => {
  const trackRef = useRef();

  useEffect(() => {
    const ctx = gsap.context(() => {
      const totalWidth = trackRef.current.scrollWidth / 2; // half of duplicated logos
      gsap.to(trackRef.current, {
        x: `-${totalWidth}px`,
        duration: 20,
        ease: 'linear',
        repeat: -1,
      });
    }, trackRef);

    return () => ctx.revert(); // cleanup
  }, []);

  return (
    <div className="w-full overflow-hidden">
      <Heading
        Heading="Trusted by Leading Companies"
        Description="Join the ranks of satisfied customers who trust BusyBucket for all their home service needs."
      />

      <div className="w-full overflow-hidden mt-8 relative h-20">
        <div
          ref={trackRef}
          className="flex gap-18 absolute left-0 top-0"
          style={{ width: 'max-content', willChange: 'transform' }}
        >
          {[...logos, ...logos].map((src, index) => (
            <img
              key={index}
              src={src}
              alt={`Company logo ${index + 1}`}
              className="h-12 w-32 object-contain"
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default TrustedCompanies;
