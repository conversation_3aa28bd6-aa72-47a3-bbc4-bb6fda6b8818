import React from 'react'
import { useNavigate } from 'react-router-dom'

const ServiceCard = ({ img, text, path }) => {
  const navigate = useNavigate();

  const handleClick = () => {
    if (path) {
      navigate(path);
    }
  };

  return (
    <div onClick={handleClick}>
      <div className="flex justify-between border-[2px] shadow-2xl border-gray-400 gap-4 sm:p-4 p-2 rounded-lg w-[90%] sm:h-24">
        <div className="rounded-lg w-[30%]">
          <img className='h-14 w-14' src={img} alt={text} />
        </div>
        <div className="flex justify-start items-center w-[70%]">
          <h3 className="font-semibold sm:text-[22px] text-[18px]">{text}</h3>
        </div>
      </div>
    </div>
  );
};

export default ServiceCard;
