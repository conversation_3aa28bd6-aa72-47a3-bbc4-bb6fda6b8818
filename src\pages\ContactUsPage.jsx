import { styled } from 'styled-components';
import { FiMapPin, FiPhone, FiMail, FiClock, FiSend } from 'react-icons/fi';

const ContactSection = styled.section`
  padding: 5rem 0;
`;

const ContactCard = styled.div`
  padding: 2rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  height: 100%;
  
  &:hover {
    transform: translateY(-5px);
  }
`;

const FormInput = styled.input`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  transition: all 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
`;

const FormTextarea = styled.textarea`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  transition: all 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
`;

const SubmitButton = styled.button`
  background-color: #3b82f6;
  color: white;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  
  &:hover {
    background-color: #2563eb;
  }
  
  svg {
    margin-right: 0.5rem;
  }
`;

const MapContainer = styled.div`
  height: 100%;
  min-height: 400px;
  background-color: #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: center;
`;

function ContactUsPage() {
  const contactInfo = [
    {
      icon: <FiMapPin size={24} />,
      title: 'Our Location',
      details: ['123 Business Avenue', 'Suite 456', 'New York, NY 10001']
    },
    {
      icon: <FiPhone size={24} />,
      title: 'Phone Number',
      details: ['+****************', '+****************']
    },
    {
      icon: <FiMail size={24} />,
      title: 'Email Address',
      details: ['<EMAIL>', '<EMAIL>']
    },
    {
      icon: <FiClock size={24} />,
      title: 'Working Hours',
      details: ['Monday-Friday: 8am-8pm', 'Saturday: 9am-5pm', 'Sunday: Closed']
    }
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle form submission logic here
    alert('Thank you for your message. We will get back to you soon!');
  };

  return (
    <div>
      <ContactSection>
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold mb-4">Contact Us</h1>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Have questions or need assistance? We're here to help. Reach out to us through any of the methods below or fill out the contact form.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {contactInfo.map((info, index) => (
              <ContactCard key={index} className="bg-white shadow-md border border-gray-100">
                <div className="bg-blue-100 p-4 rounded-full w-16 h-16 flex items-center justify-center mb-4 text-blue-600">
                  {info.icon}
                </div>
                <h3 className="text-xl font-semibold mb-3">{info.title}</h3>
                {info.details.map((detail, idx) => (
                  <p key={idx} className="text-gray-600 mb-1">{detail}</p>
                ))}
              </ContactCard>
            ))}
          </div>
          
          <div className="bg-white shadow-lg rounded-lg overflow-hidden">
            <div className="flex flex-col md:flex-row">
              <div className="md:w-1/2 p-8">
                <h2 className="text-2xl font-bold mb-6">Send Us a Message</h2>
                <form onSubmit={handleSubmit}>
                  <div className="mb-4">
                    <label htmlFor="name" className="block text-gray-700 font-medium mb-2">
                      Your Name
                    </label>
                    <FormInput 
                      type="text" 
                      id="name" 
                      placeholder="John Doe" 
                      required 
                    />
                  </div>
                  
                  <div className="mb-4">
                    <label htmlFor="email" className="block text-gray-700 font-medium mb-2">
                      Email Address
                    </label>
                    <FormInput 
                      type="email" 
                      id="email" 
                      placeholder="<EMAIL>" 
                      required 
                    />
                  </div>
                  
                  <div className="mb-4">
                    <label htmlFor="phone" className="block text-gray-700 font-medium mb-2">
                      Phone Number
                    </label>
                    <FormInput 
                      type="tel" 
                      id="phone" 
                      placeholder="+****************" 
                    />
                  </div>
                  
                  <div className="mb-4">
                    <label htmlFor="subject" className="block text-gray-700 font-medium mb-2">
                      Subject
                    </label>
                    <FormInput 
                      type="text" 
                      id="subject" 
                      placeholder="Service Inquiry" 
                      required 
                    />
                  </div>
                  
                  <div className="mb-6">
                    <label htmlFor="message" className="block text-gray-700 font-medium mb-2">
                      Your Message
                    </label>
                    <FormTextarea 
                      id="message" 
                      rows="5" 
                      placeholder="How can we help you?" 
                      required 
                    />
                  </div>
                  
                  <SubmitButton type="submit">
                    <FiSend size={18} />
                    Send Message
                  </SubmitButton>
                </form>
              </div>
              
              <div className="md:w-1/2 bg-gray-100">
                <MapContainer>
                  <iframe 
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d193595.15830869428!2d-74.11976397304903!3d40.69766374874431!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c24fa5d33f083b%3A0xc80b8f06e177fe62!2sNew%20York%2C%20NY%2C%20USA!5e0!3m2!1sen!2s!4v1645564562986!5m2!1sen!2s" 
                    width="100%" 
                    height="100%" 
                    style={{ border: 0 }} 
                    allowFullScreen="" 
                    loading="lazy"
                    title="BusyBucket Location"
                  ></iframe>
                </MapContainer>
              </div>
            </div>
          </div>
        </div>
      </ContactSection>
      
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <h2 className="text-3xl font-bold mb-6">Frequently Asked Questions</h2>
            <p className="text-gray-600 max-w-2xl mx-auto mb-12">
              Find answers to common questions about our services, booking process, and policies.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-left max-w-4xl mx-auto">
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h3 className="text-xl font-semibold mb-3">How do I schedule a service?</h3>
                <p className="text-gray-600">
                  You can schedule a service by calling our customer service line, using our online booking system, or sending us a message through the contact form above.
                </p>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h3 className="text-xl font-semibold mb-3">What is your cancellation policy?</h3>
                <p className="text-gray-600">
                  We require a 24-hour notice for cancellations. Cancellations made with less than 24 hours' notice may be subject to a cancellation fee.
                </p>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h3 className="text-xl font-semibold mb-3">Are your services insured?</h3>
                <p className="text-gray-600">
                  Yes, all our services are fully insured. Our professionals are also bonded and undergo thorough background checks.
                </p>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h3 className="text-xl font-semibold mb-3">Do you provide services on weekends?</h3>
                <p className="text-gray-600">
                  Yes, we offer services on Saturdays from 9am to 5pm. Sunday services are available for commercial clients by special arrangement.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

export default ContactUsPage;
