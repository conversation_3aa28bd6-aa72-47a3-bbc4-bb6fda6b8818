import React from 'react';
import Images from '../../assets/images';

const BookNow = () => {
    return (
        <section className="relative w-[100vw] bg-gradient-to-br sm:bg-gradient-to-r from-[#2E2E2E] to-[#000000] text-white overflow-hidden ">
            {/* Background with PlusImage pattern */}

            <div className="container mx-auto px-4 relative">
                <div className="flex flex-col md:flex-row items-center justify-between">
                    <div className="w-[20%] ">
                        <div className="absolute left-[5%] bottom-0 opacity-20 z-50 py-16 md:py-20">
                            <img
                                src={Images.PlusImage}
                                alt="Pattern"
                                className="w-[90%]"
                            />
                        </div>
                    </div>


                    {/* Left Content - Text and Button */}
                    <div className="w-full md:w-[60%] text-center md:text-center mb-10 md:mb-0 relative z-10 py-16 md:py-20">
                        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4">
                            Leave the Mess to Us –
                        </h2>
                        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-8">
                            Book Busy Bucket Today!
                        </h2>
                        <button className="bg-transparent hover:bg-white hover:text-gray-900 text-white font-semibold py-3 px-8 border-2 border-white rounded-full transition-colors duration-300 ">
                            Book Now
                        </button>
                    </div>

                    {/* Right Content - Image */}
                    <div className="w-full md:w-[20%] sm:h-[50vh]  flex sm:justify-center justify-end">
                        <img
                            src={Images.HomeCleaninLady}
                            alt="Cleaning professional"
                            className="sm:scale-150 object-contain absolute sm:bottom-8 -bottom-14 -right-4 sm:right-auto z-10"
                        />
                    </div>
                </div>
            </div>
        </section>
    );
};

export default BookNow;























