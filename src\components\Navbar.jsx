import { styled } from 'styled-components';
import { FiMenu, FiUser, FiShoppingCart, FiMapPin, FiSearch } from 'react-icons/fi';
import { useState } from 'react';
import { Link } from 'react-router-dom';
import Images from '../assets/images';

const Nav = styled.nav`
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const NavLink = styled(Link)`
  transition: all 0.2s ease;
  &:hover {
    color: #646cff;
  }
`;

const TopBanner = styled.div`
  background-color: #000;
  color: white;
  text-align: center;
  padding: 0.5rem;
  position: relative;
`;

const CloseButton = styled.button`
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: white;
  cursor: pointer;
`;

const SearchBar = styled.div`
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 9999px;
  padding: 0.5rem 1rem;
`;

function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [showBanner, setShowBanner] = useState(true);
  
  return (
    <>
      {showBanner && (
        <TopBanner>
          Sign up and get 20% off to your first service. 
          <span className="font-bold ml-1 cursor-pointer">Sign Up Now</span>
          <CloseButton onClick={() => setShowBanner(false)}>×</CloseButton>
        </TopBanner>
      )}
      <Nav className="bg-white py-4">
        <div className="container mx-auto px-4 flex justify-between items-center">
          <div className="flex items-center">
            <Link to="/" className="flex items-center">
              <img src={Images.BusyBucketLogo} alt="BusyBucket" className="h-12 w-12 mr-2" />
              {/* <span className="text-xl font-bold">BusyBucket</span> */}
            </Link>
          </div>
          
          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-6">
            <NavLink to="/" className="text-gray-800 font-medium">Home</NavLink>
            <NavLink to="/services" className="text-gray-800 font-medium">Services</NavLink>
            <NavLink to="/commercial" className="text-gray-800 font-medium">Commercial</NavLink>
            <NavLink to="/about" className="text-gray-800 font-medium">About Us</NavLink>
            <NavLink to="/contact" className="text-gray-800 font-medium">Contact Us</NavLink>
            
            <div className="flex items-center ml-4">
              <SearchBar>
                <FiMapPin className="text-gray-500 mr-2" />
                <span className="text-gray-500 mr-2">Your Location</span>
                <FiSearch className="text-gray-500 mr-2" />
                <input 
                  type="text" 
                  placeholder="Search for Home Cleaning..." 
                  className="bg-transparent border-none outline-none w-48"
                />
              </SearchBar>
            </div>
            
            <div className="flex items-center space-x-4 ml-4">
              <NavLink to="/account" className="text-gray-800">
                <FiUser size={20} />
              </NavLink>
              <NavLink to="/cart" className="text-gray-800">
                <FiShoppingCart size={20} />
              </NavLink>
            </div>
          </div>
          
          {/* Mobile Menu Button */}
          <button 
            className="md:hidden text-gray-800"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <FiMenu size={24} />
          </button>
        </div>
        
        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden bg-white py-2">
            <div className="container mx-auto px-4 flex flex-col space-y-3">
              <NavLink to="/" className="text-gray-800 py-2">Home</NavLink>
              <NavLink to="/services" className="text-gray-800 py-2">Services</NavLink>
              <NavLink to="/commercial" className="text-gray-800 py-2">Commercial</NavLink>
              <NavLink to="/about" className="text-gray-800 py-2">About Us</NavLink>
              <NavLink to="/contact" className="text-gray-800 py-2">Contact Us</NavLink>
              
              <SearchBar className="my-2">
                <FiSearch className="text-gray-500 mr-2" />
                <input 
                  type="text" 
                  placeholder="Search for Home Cleaning..." 
                  className="bg-transparent border-none outline-none w-full"
                />
              </SearchBar>
              
              <div className="flex items-center space-x-4 py-2">
                <NavLink to="/account" className="text-gray-800">
                  <FiUser size={20} className="inline mr-2" /> Account
                </NavLink>
                <NavLink to="/cart" className="text-gray-800">
                  <FiShoppingCart size={20} className="inline mr-2" /> Cart
                </NavLink>
              </div>
            </div>
          </div>
        )}
      </Nav>
    </>
  );
}

export default Navbar;
