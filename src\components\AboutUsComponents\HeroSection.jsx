import React from 'react'
import ThreeImagesGrid from '../GlobalComponents/ThreeImagesGrid'
import Images from '../../assets/images'

const heroImages = [
  Images.CleaningHero1 || '/images/cleaning-hero-1.jpg',
  Images.CleaningHero2 || '/images/cleaning-hero-2.jpg',
  Images.CleaningHero3 || '/images/cleaning-hero-3.jpg'
];

const HeroSection = () => {
  return (
    <section className=" px-4 md:px-8 lg:px-16">
      <div className="container mx-auto">
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-8">
          <div className="w-full md:w-1/2">
            <div className="w-fit">
              <p className="text-[20px] font-medium text-gray-600 mb-1">About Us</p>
              <div className="h-1 w-28 bg-amber-300 rounded-full"></div>
            </div>
            <h1 className="text-3xl md:text-4xl lg:text-[55px] font-bold mb-16 mt-4">
              Filling Your Bucket with Reliable Solutions!
            </h1>
            <button className="bg-yellow-400 hover:bg-yellow-500 text-[18px] text-black font-medium py-3 px-6  rounded-full transition-colors">
              Book a Service Now
            </button>
          </div>

          <div className="w-full md:w-1/2">
            <ThreeImagesGrid images={heroImages} />
          </div>
        </div>
      </div>
    </section>
  )
}

export default HeroSection
