import { styled } from 'styled-components';
import { FiCheckCircle, FiUsers, FiClipboard, FiStar } from 'react-icons/fi';

const HeroSection = styled.section`
  background-color: #f8fafc;
  padding: 5rem 0;
`;

const FeatureCard = styled.div`
  padding: 2rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
  }
`;

function CommercialPage() {
  const features = [
    {
      icon: <FiCheckCircle size={24} />,
      title: 'Customized Cleaning Plans',
      description: 'Tailored cleaning solutions designed to meet your specific business needs.'
    },
    {
      icon: <FiUsers size={24} />,
      title: 'Professional Staff',
      description: 'Trained and vetted cleaning professionals with commercial experience.'
    },
    {
      icon: <FiClipboard size={24} />,
      title: 'Quality Assurance',
      description: 'Regular inspections and quality checks to maintain high standards.'
    },
    {
      icon: <FiStar size={24} />,
      title: 'Eco-Friendly Options',
      description: 'Green cleaning solutions that are safe for your employees and the environment.'
    }
  ];

  return (
    <div>
      <HeroSection>
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/2 mb-8 md:mb-0">
              <h1 className="text-4xl font-bold mb-4 text-left">
                Commercial Cleaning & Maintenance Solutions
              </h1>
              <p className="text-gray-600 mb-6 text-left">
                We provide professional cleaning and maintenance services for businesses of all sizes.
                From office buildings to retail spaces, we keep your commercial property in top condition.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <button className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-md transition-colors">
                  Request a Quote
                </button>
                <button className="bg-transparent border border-blue-600 text-blue-600 font-medium py-3 px-6 rounded-md hover:bg-blue-50 transition-colors">
                  Learn More
                </button>
              </div>
            </div>
            <div className="md:w-1/2">
              <img 
                src="https://images.unsplash.com/photo-1556761175-5973dc0f32e7?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80" 
                alt="Commercial cleaning" 
                className="rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </HeroSection>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Why Choose Our Commercial Services</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              We understand the unique challenges of maintaining commercial properties and have developed
              solutions that deliver consistent results while minimizing disruption to your business.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {features.map((feature, index) => (
              <FeatureCard key={index} className="bg-white shadow-md border border-gray-100">
                <div className="bg-blue-100 p-4 rounded-full w-16 h-16 flex items-center justify-center mb-4 text-blue-600">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </FeatureCard>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/2 mb-8 md:mb-0">
              <h2 className="text-3xl font-bold mb-4 text-left">Industries We Serve</h2>
              <p className="text-gray-600 mb-6 text-left">
                Our commercial cleaning services are designed to meet the needs of various industries:
              </p>
              <ul className="space-y-3">
                <li className="flex items-center">
                  <FiCheckCircle className="text-green-500 mr-2" />
                  <span>Office Buildings & Corporate Campuses</span>
                </li>
                <li className="flex items-center">
                  <FiCheckCircle className="text-green-500 mr-2" />
                  <span>Retail Stores & Shopping Centers</span>
                </li>
                <li className="flex items-center">
                  <FiCheckCircle className="text-green-500 mr-2" />
                  <span>Healthcare Facilities & Medical Offices</span>
                </li>
                <li className="flex items-center">
                  <FiCheckCircle className="text-green-500 mr-2" />
                  <span>Educational Institutions</span>
                </li>
                <li className="flex items-center">
                  <FiCheckCircle className="text-green-500 mr-2" />
                  <span>Restaurants & Food Service</span>
                </li>
                <li className="flex items-center">
                  <FiCheckCircle className="text-green-500 mr-2" />
                  <span>Hotels & Hospitality</span>
                </li>
              </ul>
            </div>
            <div className="md:w-1/2">
              <img 
                src="https://images.unsplash.com/photo-1497366811353-6870744d04b2?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80" 
                alt="Commercial building" 
                className="rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

export default CommercialPage;
