import React, { useState } from 'react';
import Heading from '../GlobalComponents/Heading';
import { FaChevronLeft, FaChevronRight } from "react-icons/fa";

const GetToKnowUsBetter = () => {
  const [activeIndex, setActiveIndex] = useState(2);
  
  const slides = [
    {
      image: "https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
      alt: "Professional cleaning service"
    },
    {
      image: "https://images.unsplash.com/photo-1556911220-bff31c812dba?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
      alt: "Home cleaning service"
    },
    {
      image: "https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
      alt: "Professional plumbing service"
    },
    {
      image: "https://images.unsplash.com/photo-1556911220-bff31c812dba?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
      alt: "Electrical repair service"
    },
    {
      image: "https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
      alt: "Carpentry service"
    }
  ];

  const nextSlide = () => {
    setActiveIndex((prevIndex) => 
      prevIndex === slides.length - 1 ? 0 : prevIndex + 1
    );
  };

  const prevSlide = () => {
    setActiveIndex((prevIndex) => 
      prevIndex === 0 ? slides.length - 1 : prevIndex - 1
    );
  };

  // Function to get all visible slides indices
  const getVisibleSlides = () => {
    const totalSlides = slides.length;
    return {
      farLeft: (activeIndex - 2 + totalSlides) % totalSlides,
      left: (activeIndex - 1 + totalSlides) % totalSlides,
      center: activeIndex,
      right: (activeIndex + 1) % totalSlides,
      farRight: (activeIndex + 2) % totalSlides
    };
  };

  const { farLeft, left, center, right, farRight } = getVisibleSlides();

  return (
    <section className=" px-4 relative">
      <div className="container mx-auto">
        <Heading 
          Heading="Get To Know Us Better"
          Description="Discover who we are and what we stand for! Learn about our mission, values, and the exceptional services we offer to make your life easier."
        />
        
        <div className="relative mt-12 mx-auto lg:h-auto">
          {/* Navigation Buttons */}
          <button 
            onClick={prevSlide}
            className="absolute left-0 top-1/2 -translate-y-1/2 z-30 bg-white text-black p-3 rounded-full shadow-md hover:bg-gray-100 transition"
          >
            <FaChevronLeft size={16} />
          </button>
          
          <button 
            onClick={nextSlide}
            className="absolute right-0 top-1/2 -translate-y-1/2 z-30 bg-white text-black p-3 rounded-full shadow-md hover:bg-gray-100 transition"
          >
            <FaChevronRight size={16} />
          </button>
          
          {/* Stacked Carousel */}
        <div className="relative h-[40vh] lg:h-[60vh] flex items-center justify-center w-full">
  {/* Far Left Slide */}
  <div className="absolute left-[2%] sm:left-[15%] z-10 transform scale-75 transition-all duration-300">
    <div className="w-[30vw] h-[20vh] sm:w-[120px] sm:h-[160px] md:w-[160px] md:h-[200px] lg:w-48 lg:h-[40vh] rounded-3xl overflow-hidden shadow-md">
      <img 
        src={slides[farLeft].image} 
        alt={slides[farLeft].alt} 
        className="w-full h-full object-cover"
      />
    </div>
  </div>

  {/* Left Slide */}
  <div className="absolute left-[12%] sm:left-[25%] z-20 transform scale-90 transition-all duration-300">
    <div className="w-[35vw] h-[25vh] sm:w-[150px] sm:h-[200px] md:w-[180px] md:h-[240px] lg:w-56 lg:h-[45vh] rounded-3xl overflow-hidden shadow-lg">
      <img 
        src={slides[left].image} 
        alt={slides[left].alt} 
        className="w-full h-full object-cover"
      />
    </div>
  </div>

  {/* Center Slide */}
  <div className="z-30 transform scale-100 transition-all duration-300">
    <div className="w-[40vw] h-[30vh] sm:w-[180px] sm:h-[240px] md:w-[220px] md:h-[280px] lg:w-64 lg:h-[50vh] rounded-3xl overflow-hidden shadow-xl">
      <img 
        src={slides[center].image} 
        alt={slides[center].alt} 
        className="w-full h-full object-cover"
      />
    </div>
  </div>

  {/* Right Slide */}
  <div className="absolute right-[12%] sm:right-[25%] z-20 transform scale-90 transition-all duration-300">
    <div className="w-[35vw] h-[25vh] sm:w-[150px] sm:h-[200px] md:w-[180px] md:h-[240px] lg:w-56 lg:h-[45vh] rounded-3xl overflow-hidden shadow-lg">
      <img 
        src={slides[right].image} 
        alt={slides[right].alt} 
        className="w-full h-full object-cover"
      />
    </div>
  </div>

  {/* Far Right Slide */}
  <div className="absolute right-[2%] sm:right-[15%] z-10 transform scale-75 transition-all duration-300">
    <div className="w-[30vw] h-[20vh] sm:w-[120px] sm:h-[160px] md:w-[160px] md:h-[200px] lg:w-48 lg:h-[40vh] rounded-3xl overflow-hidden shadow-md">
      <img 
        src={slides[farRight].image} 
        alt={slides[farRight].alt} 
        className="w-full h-full object-cover"
      />
    </div>
  </div>
</div>

        </div>
      </div>
    </section>
  );
};

export default GetToKnowUsBetter;
