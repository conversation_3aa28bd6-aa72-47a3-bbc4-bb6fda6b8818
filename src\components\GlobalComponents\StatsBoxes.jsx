import Images from "../../assets/images";

const StatsBoxes = () => {
  const stats = [
    {
      color: 'bg-[#D6DFFA]',
      img: Images.ServingHome,
      heading: '5+ Years',
      text: 'Serving Home',
    },
    {
      color: 'bg-[#E4E4E4]',
      img: Images.HappyCustomers,
      heading: '10000+',
      text: 'Happy Customers',
    },
    {
      color: 'bg-[#FFFBD9]',
      img: Images.ProfessionalsTrained,
      heading: '500+',
      text: 'Professionals Trained',
    },
    {
      color: 'bg-[#E2FBF3]',
      img: Images.OperatingCities,
      heading: '20+',
      text: 'Operating In Multiple Cities',
    },
    {
      color: 'bg-[#FFF4D6]',
      img: Images.HomeServices,
      heading: '30+',
      text: 'Offering A Range Of Home Services',
    },
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-5 p-4">
      {stats.map((item, index) => (
        <div
          key={index}
          className={`rounded-4xl p-4 text-center ${item.color} shadow-md flex flex-col items-center`}
        >
          <img src={item.img} alt="icon" className="w-10 h-10 mb-2" />
          <h2 className="text-[35px] font-bold">{item.heading}</h2>
          <p className="text-[18px]">{item.text}</p>
        </div>
      ))}
    </div>
  );
};

export default StatsBoxes;
