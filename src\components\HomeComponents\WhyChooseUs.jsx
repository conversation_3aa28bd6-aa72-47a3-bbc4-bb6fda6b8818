import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>lock, FiDollarSign, FiThumbsUp, FiGrid } from 'react-icons/fi';
import Heading from '../GlobalComponents/Heading';
import Images from '../../assets/images';

const WhyChooseUs = () => {
  const benefits = [
    {
      img: Images.VerifiedProfessionals,
      title: "Verified Professionals",
      description: "All our experts are background-checked, trained, and certified.",
    },
    {
      img: Images.FlexibleScheduling,
      title: "Flexible Scheduling",
      description: "Book services at your convenience—any time, any day.",
    },
    {
      img: Images.AffordablePricing,
      title: "Affordable Pricing",
      description: "Transparent pricing with no hidden charges.",
    },
    {
      img: Images.SatisfactionGuarantee,
      title: "100% Satisfaction Guarantee",
      description: "We ensure every service is completed to your satisfaction.",
    },
    {
      img: Images.WideRangeOfServices,
      title: "Wide Range of Services",
      description: "Covering everything from home cleaning to electrical & plumbing needs.",
    },
  ];

  return (
    <section className=" px-2 relative  w-[100vw]">
      {/* Background pattern image - will be replaced with actual image */}
      <div className="absolute inset-0 z-0 opacity-10">
        <div className="w-full h-full "></div>
      </div>

      <div className="w-full flex flex-col justify-center items-center relative z-10">
        <Heading
          Heading="Why Choose Us?"
          Description="Choose us for reliable, eco-friendly cleaning and top-notch service, tailored to your needs for a spotless home every time."
        />

        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-8 mt-12  w-[90%]">
          {benefits.map((benefit, index) => (
            <div
              key={index}
              className="bg-white rounded-3xl p-6 sm:py-12 shadow-sm flex flex-col items-center text-center"
            >
              <div className="w-16 h-16 mb-4 flex items-center justify-center">
                <div className="w-16 h-16 rounded-full flex items-center justify-center">
                  <img src={benefit.img} alt={benefit.title} />
                </div>
              </div>
              <h3 className="text-[22px] font-semibold mb-2 sm:h-16 ">{benefit.title}</h3>
              <p className="text-gray-600 text-[16px]">{benefit.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default WhyChooseUs;
