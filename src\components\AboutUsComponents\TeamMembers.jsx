import React from 'react'
import Heading from '../GlobalComponents/Heading';

const TeamMembers = () => {

    
    const teamMembers = [
    {
      name: '<PERSON>',
      position: 'CEO & Founder',
      image: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80'
    },
    {
      name: '<PERSON>',
      position: 'Operations Manager',
      image: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80'
    },
    {
      name: '<PERSON>',
      position: 'Customer Service Director',
      image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80'
    },
    {
      name: '<PERSON>',
      position: 'Quality Assurance Manager',
      image: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80'
    }
  ];

  return (
   <div>
       <section className=" ">
        <div className="container mx-auto px-4 space-y-8">
         <Heading 
            Heading="Meet Busy Bucket Expert"
            Description="Our skilled and dedicated team ensures top-quality service, reliability, and customer satisfaction every time."
          />
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 px-10">
            {teamMembers.map((member, index) => (
              <div key={index} className="bg-white shadow-md rounded-md overflow-hidden transition-all duration-300 ease-in-out hover:-translate-y-1 hover:shadow-[0_10px_25px_rgba(0,0,0,0.1)]">
                <img 
                  src={member.image} 
                  alt={member.name} 
                  className="w-full h-72 object-cover "
                />
                <div className="p-4">
                  <h3 className="text-xl font-semibold mb-1">{member.name}</h3>
                  <p className="text-gray-600">{member.position}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  )
}

export default TeamMembers
