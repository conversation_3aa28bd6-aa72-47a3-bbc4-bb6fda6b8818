import React from 'react'
import Heading from '../GlobalComponents/Heading'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Navigation, Pagination } from 'swiper/modules';
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'
import { FaChevronLeft } from "react-icons/fa";
import { FaChevronRight } from "react-icons/fa";
import Images from '../../assets/images'
import { FaStar } from "react-icons/fa";

const slides = [
    {
  title: 'Spa Treatment',
  image: Images.VaccumCleaner,
  price: 999,
  reviews: 120,
},
    {
  title: 'Spa Treatment',
  image: Images.VaccumCleaner,
  price: 999,
  reviews: 120,
},
    {
  title: 'Spa Treatment',
  image: Images.VaccumCleaner,
  price: 999,
  reviews: 120,
},
    {
  title: 'Spa Treatment',
  image: Images.VaccumCleaner,
  price: 999,
  reviews: 120,
},
    {
  title: 'Spa Treatment',
  image: Images.VaccumCleaner,
  price: 999,
  reviews: 120,
},
    {
  title: 'Spa Treatment',
  image: Images.VaccumCleaner,
  price: 999,
  reviews: 120,
},
    {
  title: 'Spa Treatment',
  image: Images.VaccumCleaner,
  price: 999,
  reviews: 120,
},

]

const TopServices = () => {
  return (
    <div className='w-[100vw] relative flex flex-col items-center gap-10'>
        <Heading
            Heading="Top-Requested Services"
            Description="Our top-requested services include cleaning, plumbing, electrical repairs, and more, tailored to your needs."
        />

          {/* Swiper Left/Right Arrows */}
                    <div className="absolute sm:left-2 -left-4 top-[50%] z-10 -translate-y-1/2 pl-4">
                        <button className="top-swiper-prev bg-white text-black p-[12px] rounded-full shadow hover:bg-gray-200 transition">
                            <FaChevronLeft size={16} />
                        </button>
                    </div>
                    <div className="absolute sm:right-2 -right-4 top-[50%] z-10 -translate-y-1/2 pr-4">
                        <button className="top-swiper-next bg-white text-black p-[12px] rounded-full shadow hover:bg-gray-200 transition">
                            <FaChevronRight size={18} />
                        </button>
                    </div>
        
                    {/* Swiper */}
                    <div className="relative w-[90%]">
                        <Swiper
                            modules={[Navigation, Pagination]}
                            navigation={{
                                nextEl: '.top-swiper-next',
                                prevEl: '.top-swiper-prev',
                            }}
                            pagination={{ clickable: true }}
                            spaceBetween={30}
                            slidesPerView={5}
                            loop={true}
                            className="top-swiper 
                                [&_.swiper-pagination]:!bottom-0 
                                [&_.swiper-pagination]:!relative 
                                [&_.swiper-pagination]:mt-2
                                [&_.swiper-pagination-bullet]:!bg-gray-400 
                                [&_.swiper-pagination-bullet-active]:!bg-black"
                            breakpoints={{
                                0: {slidesPerView: 1,},
                                768: {slidesPerView: 2,},
                                1024: {slidesPerView: 5,},
                            }}
                        >
                           {slides.map((slide, i) => (
  <SwiperSlide key={i}>
    <div className="flex flex-col rounded-2xl shadow-lg overflow-hidden bg-white w-full h-full my-2">
      {/* Top Image */}
      <div className="h-[80%] w-full">
        <img
          src={slide.image}
          alt={slide.title}
          className="w-full h-full object-cover rounded-t-2xl"
        />
      </div>

      {/* Bottom Content */}
      <div className="flex justify-between items-start px-4 py-3 text-black text-sm h-[20%] ">
        {/* Left: Title + Stars + Rating */}
        <div >
          <h3 className="font-semibold text-base mb-1">{slide.title}</h3>
          <div className="flex items-center gap-1 text-yellow-500 text-xs">
            {[...Array(5)].map((_, index) => (
              <FaStar key={index} />
            ))}
          </div>
            <span className="text-gray-600 text-xs ml-1">5+ rating</span>
        </div>

        {/* Right: Price + Reviews */}
        <div className="text-right">
          <p className="text-base font-semibold">₹{slide.price}</p>
          <p className="text-gray-500 text-xs">{slide.reviews} reviews</p>
        </div>
      </div>
    </div>
  </SwiperSlide>
))}

                        </Swiper>
                         {/* <div className="swiper-pagination bottom-[30px] absolute w-full text-center"></div> */}
                    </div>
      
    </div>
  )
}

export default TopServices
