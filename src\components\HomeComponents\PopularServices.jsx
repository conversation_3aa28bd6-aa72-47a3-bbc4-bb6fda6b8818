import React from 'react'
import Heading from '../GlobalComponents/Heading'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Navigation, Pagination } from 'swiper/modules';
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'
import { FaChevronLeft } from "react-icons/fa";
import { FaChevronRight } from "react-icons/fa";
import Images from '../../assets/images'

const slides = [
    {
        heading: 'Home Cleaning',
        subheading: 'Our expert cleaning team ensures your home is spotless, from top to bottom, leaving you with more time to enjoy the things that matter most.',
        bgImage: Images.PopularCardBg,
        bgColour: '#0080A6',
        rightImage: Images.HomeCleaninLady,
    },
    {
        heading: 'Electrical Repairs',
        subheading: 'Expert electrical repairs to ensure safety and efficiency, from fixing faults to upgrading systems.',
        bgImage: Images.PopularCardBg,
        bgColour: '#922130',
        rightImage: Images.PlumberMan,
    },
    {
        heading: 'Plumbing',
        subheading: 'Reliable plumbing services for repairs, installations, & maintenance to keep your systems running smoothly',
        bgImage: Images.PopularCardBg,
        bgColour: '#3563E9',
        rightImage: Images.ElectricianMan,
    },
    {
        heading: 'Room Service',
        subheading: 'Experience the convenience of having your home cleaned by our expert team.',
        bgImage: Images.PopularCardBg,
        bgColour: '#0080A6',
        rightImage: Images.HomeCleaninLady,
    },
]

const PopularServices = () => {
    return (
        <div className=" w-[100vw] flex flex-col items-center gap-10  relative ">
            <Heading
                Heading="Popular Services"
                Description="Popular services include deep cleaning, regular upkeep, move-in/move-out, and post-renovation cleaning for a spotless home."
            />

            {/* Swiper Left/Right Arrows */}
            <div className="absolute sm:left-2 -left-4 top-[60%] z-10 -translate-y-1/2 pl-4">
                <button className="popular-swiper-prev bg-white text-black p-[12px] rounded-full shadow hover:bg-gray-200 transition">
                    <FaChevronLeft size={16} />
                </button>
            </div>
            <div className="absolute sm:right-2 -right-4 top-[60%] z-10 -translate-y-1/2 pr-4">
                <button className="popular-swiper-next bg-white text-black p-[12px] rounded-full shadow hover:bg-gray-200 transition">
                    <FaChevronRight size={18} />
                </button>
            </div>

            {/* Swiper */}
            <div className="relative w-[90%] sm:mb-4">
                <Swiper
                    modules={[Navigation, Pagination]}
                    navigation={{
                        nextEl: '.popular-swiper-next',
                        prevEl: '.popular-swiper-prev',
                    }}
                    pagination={{ clickable: true }}
                    spaceBetween={30}
                    slidesPerView={3}
                    loop={true}
                    className="popular-swiper 
                    [&_.swiper-pagination]:!bottom-0 
                    [&_.swiper-pagination]:!relative 
                    [&_.swiper-pagination]:mt-6 
                    [&_.swiper-pagination-bullet]:!bg-gray-400 
                    [&_.swiper-pagination-bullet-active]:!bg-black"
                    breakpoints={{
                        0: { slidesPerView: 1, },
                        768: { slidesPerView: 2, },
                        1024: { slidesPerView: 3, },
                    }}
                >
                    {slides.map((slide, i) => (
                        <SwiperSlide key={i}>
                            <div
                                className={`w-full sm:h-[25vh] md:h-[28vh] lg:h-[20vh] xl:h-[37vh] flex rounded-xl overflow-hidden bg-cover bg-center text-white `}
                                style={{
                                    backgroundImage: `url(${slide.bgImage})`,
                                    backgroundColor: slide.bgColour,
                                }}
                            >
                                {/* Left Content */}
                                <div className="w-[60%] p-4 md:py-[20px] flex flex-col justify-between  ">
                                    <div>

                                        <h2 className="text-[27px] sm:leading-none leading-8 md:text-[27px] font-bold mb-4 whitespace-nowrap">{slide.heading}</h2>
                                        <p className="text-[16px] md:text-[13px] mb-3 md:mb-4">{slide.subheading}</p>
                                    </div>
                                    <button className="bg-white text-black px-6 py-2 md:px-6 md:py-2 rounded-full font-semibold w-fit text-sm  ">
                                        Book Now
                                    </button>
                                </div>

                                {/* Right Image */}
                                <div className="w-[40%] h-full relative">
                                    <img
                                        src={slide.rightImage}
                                        alt="slide"
                                        className="h-70 w-full object-cover absolute bottom-0 xl:static xl:object-bottom"

                                    />
                                </div>

                            </div>
                        </SwiperSlide>
                    ))}
                </Swiper>
                {/* <div className="swiper-pagination bottom-[30px] absolute w-full text-center"></div> */}
            </div>
        </div>
    )
}

export default PopularServices
