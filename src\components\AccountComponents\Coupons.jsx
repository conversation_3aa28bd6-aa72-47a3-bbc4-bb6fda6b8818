import React from 'react'
import { FaRegCopy } from "react-icons/fa";
import { FiArrowRight } from "react-icons/fi";

const coupons = [
  {
    id: 1,
    discount: "5% OFF",
    code: "NEWCUSTOMER_1234",
    validFrom: "05/08/2025 04:00",
    validTo: "09/08/2025 12:00",
    details: [
      "For all services.",
      "Combinations: Get 20% off when you spend over ₹169.00 or get 15% off when you spend over ₹89.00."
    ]
  },
  {
    id: 2,
    discount: "10% OFF",
    code: "REFER_5678",
    validFrom: "05/08/2025 04:00",
    validTo: "09/08/2025 12:00",
    details: [
      "For all services.",
      "Combinations: Get 20% off when you spend over ₹169.00 or get 15% off when you spend over ₹89.00."
    ]
  },
  {
    id: 3,
    discount: "15% OFF",
    code: "BUSYBUCKET_9012",
    validFrom: "05/08/2025 04:00",
    validTo: "09/08/2025 12:00",
    details: [
      "For all services.",
      "Combinations: Get 20% off when you spend over ₹169.00 or get 15% off when you spend over ₹89.00."
    ]
  },
  {
    id: 4,
    discount: "20% OFF",
    code: "BUSYBUCKET_9012",
    validFrom: "05/08/2025 04:00",
    validTo: "09/08/2025 12:00",
    details: [
      "For all services.",
      "Combinations: Get 20% off when you spend over ₹169.00 or get 15% off when you spend over ₹89.00."
    ]
  },
  {
    id: 5,
    discount: "25% OFF",
    code: "BUSYBUCKET_9012",
    validFrom: "05/08/2025 04:00",
    validTo: "09/08/2025 12:00",
    details: [
      "For all services.",
      "Combinations: Get 20% off when you spend over ₹169.00 or get 15% off when you spend over ₹89.00."
    ]
  },
  {
    id: 6,
    discount: "30% OFF",
    code: "BUSYBUCKET_9012",
    validFrom: "05/08/2025 04:00",
    validTo: "09/08/2025 12:00",
    details: [
      "For all services.",
      "Combinations: Get 20% off when you spend over ₹169.00 or get 15% off when you spend over ₹89.00."
    ]
  }
];

const Coupons = () => {
    return (
        <div>
            <h1 className="text-2xl font-medium mb-6 text-center">Coupons</h1>

            <div className="max-w-4xl mx-auto rounded-lg border-[1px] border-gray-400 p-4">
                
                 <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {coupons.map((coupon) => (
          <div key={coupon.id} className="border-[1px] border-gray-400 rounded-lg p-4 shadow-sm bg-white">
            <h2 className="text-xl font-bold text-gray-800">{coupon.discount}</h2>
            <p className="text-sm text-gray-600 mb-1">FOR WHOLE ORDER</p>
            <p className="text-sm text-blue-600 font-semibold mb-2">
              Code: {coupon.code}
            </p>

            <div className="flex items-center gap-4 mb-3 text-sm text-gray-700">
              <button
                onClick={() => handleCopy(coupon.code)}
                className="flex items-center gap-1 hover:text-black transition"
              >
                <FaRegCopy className="text-sm" /> Copy
              </button>
              <button className="flex items-center gap-1 hover:text-black transition">
                Apply <FiArrowRight className="text-sm" />
              </button>
            </div>

            <ul className="text-xs text-gray-600 list-disc list-inside space-y-1">
              <li>
                {coupon.validFrom} – {coupon.validTo}
              </li>
              {coupon.details.map((d, i) => (
                <li key={i}>{d}</li>
              ))}
            </ul>
          </div>
        ))}
      </div>
            </div>
        </div>
    )
}

export default Coupons
