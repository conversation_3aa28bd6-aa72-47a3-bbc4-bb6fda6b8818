import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Layout from './components/Layout';
import HomePage from './pages/HomePage';
import ServicesPage from './pages/ServicesPage';
import CommercialPage from './pages/CommercialPage';
import AboutUsPage from './pages/AboutUsPage';
import ContactUsPage from './pages/ContactUsPage';
import './App.css';
import Login from './pages/Login';
import DeepCleaning from './pages/DeepCleaning';
// import Address from './pages/Address';
import Checkout from './pages/Checkout';
import Thankyou from './components/CheckoutComponents/Thankyou';
import Account from './pages/Account';

function App() {
  return (
    <Router>
      <Routes>
        {/* Routes with layout */}
        <Route element={<Layout />}>
          <Route path="/" element={<HomePage />} />
          <Route path="/services" element={<ServicesPage />} />
          <Route path="/commercial" element={<CommercialPage />} />
          <Route path="/about" element={<AboutUsPage />} />
          <Route path="/contact" element={<ContactUsPage />} />
          <Route path="/deepcleaning" element={<DeepCleaning />} />
          {/* <Route path="/address" element={<Address />} /> */}
          <Route path="/checkout" element={<Checkout />} />
          <Route path="/thankyou" element={<Thankyou />} />
          <Route path="/account/*" element={<Account />} />
        </Route>

        {/* Route without layout */}
        <Route path="/login" element={<Login />} />
      </Routes>
    </Router>
  );
}

export default App;
