import React, { useState } from "react";
import { FaRegCopy } from "react-icons/fa";
import { FiArrowRight } from "react-icons/fi";
import { BsWhatsapp } from "react-icons/bs";
import { FaArrowRight } from "react-icons/fa";
import { IoMdLock } from "react-icons/io";
import Images from "../../assets/images";

const ReferAFriend = () => {
  const [referralLink] = useState("voucherify.io");

  const handleCopy = () => {
    navigator.clipboard.writeText(referralLink);
    alert("Referral link copied!");
  };

  return (
    <div>
      <h1 className="text-2xl font-medium mb-6 text-center">Refer a Friend</h1>

      <div className="max-w-4xl mx-auto rounded-lg border border-gray-300 p-4 grid grid-cols-1 lg:grid-cols-[2fr_1fr] gap-6 bg-white">
        {/* Left Section */}
        <div>
          {/* Top Yellow Box */}
          <div className="bg-yellow-300 text-sm font-medium rounded-md px-4 py-2 mb-4">
            You Get ₹100 off on service
          </div>

          {/* Reward Steps */}
          <div className="bg-white border border-gray-300 rounded-md p-4 mb-4">
            <div className="flex flex-wrap items-center justify-between mb-3">
              <div className="flex items-center gap-4 text-sm font-semibold">
                <span className="px-1 py-1">REWARDS</span>
                <IoMdLock />
                <span className="px-1 py-1">FREE SAMPLE</span>
                <IoMdLock />
                <span className="px-1 py-1">SURPRISE GIFT</span>
                <IoMdLock />
                <span className="px-1 py-1">SURPRISE GIFT</span>
              </div>
            </div>

            {/* Fake progress bar */}
            <div className="flex items-center gap-2 my-3">
              {[1, 2, 3, 4, 5, 6, 7, 8].map((index, num) => (
                <div
                className=" flex-1"
                key={index}
                >
                     {(index === 1 || index === 3 || index === 8) ? (
        <h1 className="text-sm ">{index }</h1>) : (<h1 className="text-sm invisible ">{index }</h1>
      )}

                    <div
                    className={`h-2 ${num === 0 ? "rounded-l-full" :""}  ${num === 7 ? "rounded-r-full" :""} ${num < 3 ? "bg-green-500" : "bg-gray-200"}`}
                    />
                </div>
                ))}
            </div>
                <span className="text-sm text-black font-semibold">Invite 3 more to get a ₹100 once your service</span>
          </div>

          {/* Stat Boxes */}
          <div className="grid grid-cols-2 sm:grid-cols-2 gap-4 text-center text-sm">
            <div className="border flex justify-between border-gray-300 rounded-md py-4 pl-3 pr-6">
                <div className="text-left ">
                    <div className="text-xl font-bold  mb-2">0</div>
                    <div className="text-gray-600 mt-1">FRIENDS INVITED</div>
                </div>
                <FaArrowRight size={25}/>
            </div>
            <div className="border flex justify-between border-gray-300 rounded-md py-4 pl-3 pr-6">
                <div className="text-left">
                    <div className="text-xl font-bold mb-2">0</div>
                    <div className="text-gray-600 mt-1">FRIENDS REFERRED</div>
                </div>
                <FaArrowRight size={25}/>
            </div>
            <div className="border border-gray-300 rounded-md py-4 pl-3 pr-6 text-left">
              <div className="text-xl font-bold">₹ 0</div>
              <div className="text-gray-600 mt-1">TOTAL EARNED</div>
            </div>
            <div className="border border-gray-300 rounded-md py-4 pl-3 pr-6 text-left">
              <div className="text-xl font-bold">₹ 0</div>
              <div className="text-gray-600 mt-1">PENDING CREDITS</div>
            </div>
          </div>

          {/* Pending Invites */}
          <div className="flex items-center mt-4 border border-gray-300 rounded-md px-4 py-2 text-sm">
            <p className="my-2 pb-4 border-b border-gray-300 w-full">PENDING INVITES {"  "} (0)</p>
          </div>
        </div>

        {/* Right Sidebar */}
        <div className="bg-yellow-100 rounded-md p-4">
          <h2 className="text-md font-semibold mb-2">Refer and Get FREE Services</h2>
          <p className="text-sm text-gray-700 mb-8">
            Get your referral link to give your friends a ₹100 off discount when
            they try Busy Bucket services. If they use it, we'll reward you with ₹100 too!
          </p>

          <div className="">
            <p className="text-sm text-gray-700 mb-4 w-full text-center">
            Or copy your personal link
          </p>

          <div className="flex  h-10 items-center bg-white rounded-lg overflow-hidden shadow border border-gray-400 mb-3">
            <input
              type="text"
              readOnly
              value={referralLink}
              className="px-3 py-2 w-full text-sm outline-none"
            />
            <button
              onClick={handleCopy}
              className="px-3 py-2 h-full bg-black text-white text-sm"
            >
              <FaRegCopy />
            </button>
          </div>
          </div>


          <div className="flex flex-col gap-4 text-sm mt-8">
            <span className="font-semibold">Refer via</span>
            <a href={`https://wa.me/?text=Try Busy Bucket! Use this link: ${referralLink}`} target="_blank" rel="noopener noreferrer">
              {/* <BsWhatsapp className="text-green-600 text-lg" /> */}
              <img className="scale-125" src={Images.WhatsApp} alt="" />
            </a>
          <a href="#" className="text-xs text-gray-600 underline mt-2 inline-block">
            Terms & Conditions
          </a>
          </div>

        </div>
      </div>
    </div>
  );
};

export default ReferAFriend;
