import React, { useState } from 'react'
import Images from '../../assets/images';

const PaymentMethods = () => {
     const [paymentMode , setPaymentMode] = useState("upi")

     
  return (
     <div>

                    <div className='flex sm:flex-row  flex-col gap-10 justify-between '>

                        {/* Order Summary */}
                        <div className="mb-8 sm:w-[45%] w-auto">
                            <h3 className="font-medium mb-4">Ordered Summary</h3>

                            {/* Service Items */}
                            <div className="space-y-4 mb-6">
                                <div className="flex items-center justify-between bg-[#F6F6F6] p-4 rounded-md">
                                    <div className="flex items-center">
                                        <img
                                            src="https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=60"
                                            alt="Water Tank Cleaning"
                                            className="w-12 h-12 object-cover rounded-md mr-3"
                                        />
                                        <span>Water Tank Cleaning</span>
                                    </div>
                                    <span className="font-medium">₹399.00</span>
                                </div>

                                <div className="flex items-center justify-between bg-[#F6F6F6] p-4 rounded-md">
                                    <div className="flex items-center">
                                        <img
                                            src="https://images.unsplash.com/photo-1584622650111-993a426fbf0a?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=60"
                                            alt="BHK Cleaning"
                                            className="w-12 h-12 object-cover rounded-md mr-3"
                                        />
                                        <span>3 BHK (Bedroom, Hall and Kitchen)</span>
                                    </div>
                                    <span className="font-medium">₹3,999.00</span>
                                </div>
                            </div>

                            {/* Address */}
                            <div className="mb-4">
                                <h4 className="text-sm text-gray-500 mb-1">Address</h4>
                                <p className="text-sm">101 Dusty Township, Jacksonville, FL 40233</p>
                            </div>

                            {/* Price Breakdown */}
                            <div className="space-y-2 mb-4">
                                <div className="flex justify-between">
                                    <span className="text-black">Subtotal</span>
                                    <span>₹4,397.00</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600">Discount</span>
                                    <span className="text-green-600">-₹499.70</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600">GST</span>
                                    <span>₹703.52</span>
                                </div>
                                <div className="flex justify-between font-semibold">
                                    <span>Total Investment</span>
                                    <span>₹4,600.82</span>
                                </div>
                            </div>

                            {/* Cancellation Policy */}
                            <div className="bg-gray-50 p-2 rounded-lg text-sm mb-4">
                                <h4 className="font-medium mb-1">Cancellation Policy</h4>
                                <p className="text-gray-600 text-xs">Free cancellation if done more than 2 hrs before the service. If the cancellation is delayed, it will be charged otherwise.</p>
                                <p className="text-xs mt-2">
                                    By proceeding, you agree to our <span className="text-blue-600 underline">Cancellation Policy</span>
                                </p>
                            </div>

                            {/* Additional Services */}
                            <div className=''>
                                <h3 className="font-medium mb-4">Add more service</h3>
                                <div className="grid grid-cols-3 gap-4">
                                    {[
                                        { name: "Sofa service", price: "₹400", icon: "🛋️" },
                                        { name: "Tub service", price: "₹300", icon: "🛁" },
                                        { name: "Sink service", price: "₹500", icon: "🚰" }
                                    ].map((service, index) => (
                                        <div key={index} className="border border-gray-200 rounded-lg p-3 text-center">
                                            <div className="text-2xl mb-1">{service.icon}</div>
                                            <p className="text-sm font-medium">{service.name}</p>
                                            <p className="text-sm mb-2">{service.price}</p>
                                            <button className="bg-black text-white text-xs py-1 px-3 rounded-md">Add</button>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>

                        {/* Payment Methods */}
                        <div className="mb-8 sm:w-[45%] w-auto">
                            <h2 className="text-xl font-semibold mb-6">Payment</h2>
                            <div className="flex border-b border-gray-200 mb-4">
                                <button onClick={()=>setPaymentMode("card")} className={`py-2 px-4 ${paymentMode === "card" ? "border-b-2 border-black text-black" : " text-gray-500"}  font-medium mr-4`}>Credit Card</button>
                                <button onClick={()=>setPaymentMode("paypal")} className={`py-2 px-4 ${paymentMode === "paypal" ? "border-b-2 border-black text-black" : " text-gray-500"}  font-medium mr-4`}>PayPal</button>
                                <button onClick={()=>setPaymentMode("upi")} className={`py-2 px-4 ${paymentMode === "upi" ? "border-b-2 border-black text-black" : " text-gray-500"}  font-medium mr-4`}>UPI</button>
                                <button onClick={()=>setPaymentMode("netBanking")} className={`py-2 px-4 ${paymentMode === "netBanking" ? "border-b-2 border-black text-black" : " text-gray-500"}  font-medium mr-4`}>Net Banking</button>
                            </div>

                            {/* Credit Card Form */}
                            <div className="mb-6">
                                <div className="mb-4">
                                    <img
                                        src={Images.CreditCard}
                                        alt="Credit Card"
                                        className="w-full  max-w-xs rounded-lg"
                                    />
                                </div>

                               {paymentMode === "card" ? ( <div className="grid grid-cols-2 gap-4 mb-4">
                                    <div className='grid col-span-2'>
                                        <label className="block text-sm text-black mb-1">Cardholder Name</label>
                                        <input
                                            type="text"
                                            className="w-full p-2 border border-gray-200 rounded-lg"
                                            placeholder="John Smith"
                                        />
                                    </div>
                                    <div className='grid col-span-2'>
                                        <label className="block text-sm text-black mb-1">Card Number</label>
                                        <input
                                            type="text"
                                            className="w-full p-2 border border-gray-200 rounded-lg"
                                            placeholder="XXXX XXXX XXXX"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm text-black mb-1">Exp Date</label>
                                        <input
                                            type="date"
                                            className="w-full p-2 border border-gray-200 rounded-lg"
                                            placeholder="123"
                                            maxLength="3"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm text-black mb-1">CVV</label>
                                        <input
                                            type="text"
                                            className="w-full p-2 border border-gray-200 rounded-lg"
                                            placeholder="123"
                                            maxLength="3"
                                        />
                                    </div>
                                </div>) : <></>}

                               { paymentMode === "upi" ? (<div className="grid grid-cols-2 gap-4 mb-4">
                                    <div className='grid col-span-2'>
                                        <label className="block text-sm text-black mb-1">Google Pay UPI</label>
                                        <input
                                            type="text"
                                            className="w-full p-2 border border-gray-200 rounded-lg"
                                            placeholder="12345678@ybl"
                                        />
                                    </div>
                                    <div className='grid col-span-2'>
                                        <label className="block text-sm text-black mb-1">Add a New UPI ID</label>
                                        <input
                                            type="text"
                                            className="w-full p-2 border border-gray-200 rounded-lg"
                                            placeholder="12345678@ybl"
                                        />
                                    </div>
                                   
                                </div>) : <></>}
                                <div className="mb-4">
                                    <div className="flex items-center">
                                        <input
                                            type="checkbox"
                                            id="saveAddress"
                                            className="mr-2"
                                        />
                                        <label htmlFor="saveAddress" className="text-sm">Save as billing address</label>
                                    </div>
                                </div>
                                
                            </div>
                        </div>
                    </div>


                </div>
  )
}

export default PaymentMethods
