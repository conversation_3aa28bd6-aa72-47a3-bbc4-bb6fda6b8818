import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, FiX } from 'react-icons/fi';
import { FaBucket } from "react-icons/fa6";
import Images from '../../assets/images';
import { GiSettingsKnobs } from "react-icons/gi";
import Steps from './Steps';

const rating = [
      {
        id: 1,
        name: "<PERSON>",
        rating: 5,
        comment: "Thank you for doing such a good job of cleaning. I informed them once, and they cleaned as per my request. The best part is they didn't make a mess. Thank you, Urban Company, for training your team so well.",
        date: "November 24, 2024",
        service: "Classic (Furnished)"
      },
      {
        id: 2,
        name: "<PERSON>",
        rating: 5,
        comment: "<PERSON><PERSON> and team were extremely professional and did an excellent job cleaning the entire house well. They are perfect in their job and will definitely recommend them for future requirements!",
        date: "November 15, 2024",
        service: "Premium (Furnished)"
      },
      {
        id: 3,
        name: "Anosh <PERSON>",
        rating: 4,
        comment: "Excellent work done by the team. My house was closed for more than a year and the way they did their job was fantastic. Each and every corner was well cleaned. Thank you for the service.",
        date: "November 15, 2024",
        service: "Premium (Furnished)"
      },
      {
        id: 4,
        name: "<PERSON><PERSON><PERSON>",
        rating: 5,
        comment: "<PERSON>ga <PERSON>im & team are great work. They completed the work with full excellency & also devoted extra time. No work was said no and <PERSON>im ji was the one to inspect everything. Three cheers thanks Salim ji.",
        date: "October 17, 2024",
        service: "Platinum (Furnished)"
      },
      {
        id: 5,
        name: "Chintan Shah",
        rating: 4,
        comment: "Suraj & Neeraj, both are extremely co-operative and helpful. The way they have cleaned the house is amazing. They both are complete package and compliment each other work. ",
        date: "September 18, 2023",
        service: "Platinum (Furnished)"
      },
      {
        id: 6,
        name: "Suchismita Datta",
        rating: 5,
        comment: "Came 1 hr late. Compared to my past two experiences of full house cleaning the efficiency of this team of people was less and detailing of the work was lacking. They were less active in nature",
        date: "September 10, 2023",
        service: "Classic (Furnished)"
      },
      {
        id: 7,
        name: "Chintan Shah",
        rating: 4,
        comment: "Suraj & Neeraj, both are extremely co-operative and helpful. The way they have cleaned the house is amazing. They both are complete package and compliment each other work. ",
        date: "September 18, 2023",
        service: "Platinum (Furnished)"
      },
      {
        id: 8,
        name: "Suchismita Datta",
        rating: 5,
        comment: "Came 1 hr late. Compared to my past two experiences of full house cleaning the efficiency of this team of people was less and detailing of the work was lacking. They were less active in nature",
        date: "September 10, 2023",
        service: "Classic (Furnished)"
      },
    ];

const ServiceDetails = () => {

  const [activeTab, setActiveTab] = useState('details');
  const [formData, setFormData] = useState({
    name: '',
    mobile: '', serviceName: 'Deep Home Cleaning',
    location: '', getUpdates: false
  });
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const [visibleReviews, setVisibleReviews] = useState(4);
  // const [showSteps, setShowSteps] = useState(false);
  // const [selectedService, setSelectedService] = useState(null);

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('Form submitted:', formData);    // Handle form submission logic here
  };

  const handleBookNow = (service) => {
    setSelectedService(service);
    setShowSteps(true);
  };

  return (
    <section className="py-8 px-4 md:px-8 lg:px-16">
      {/* Show Steps modal when showSteps is true */}
      {/* {showSteps && (
        <Steps 
          onClose={() => setShowSteps(false)} 
          service={selectedService} 
        />
      )} */}

      <div className="flex flex-col lg:flex-row gap-8">
        {/* Left Content (2/3 width on desktop) */}
        <div className="w-full lg:w-2/3">
          {/* Tabs */}
          <div className="border-b border-gray-200 mb-6 ">
            <div className="flex text-[18px] md:w-[80%]">
              <button
                className={`py-3 px-8 font-medium w-[50%]  ${activeTab === 'details' ? 'border-b-2 border-black text-black' : 'text-gray-500'
                  }`}
                onClick={() => setActiveTab('details')}
              >
                Service Details
              </button>
              <button
                className={`py-3 px-8 font-medium w-[50%] ${activeTab === 'ratings' ? 'border-b-2 border-black text-black' : 'text-gray-500'
                  }`}
                onClick={() => setActiveTab('ratings')}
              >
                Rating & Reviews
              </button>
            </div>
          </div>

          {/* Service Details Content */}
          {activeTab === 'details' && (
            <div>
              <h2 className="text-xl font-semibold mb-2">Deep Home Cleaning</h2>
              <p className="text-[#7A7A7A] text-sm mb-6">
                Deep home cleaning is a thorough and intensive cleaning process designed to address areas of the home
                that are often neglected during regular cleaning routines. It focuses on removing built-up dirt, grime, &
                allergens to ensure a healthier and more hygienic living environment. This process typically involves detailed
                attention to every corner, surface, & fixture in the home.
              </p>

              {/* Key Components */}
              <div className="mb-6">
                <h3 className="text-md font-semibold mb-3">Key Components of Deep Home Cleaning</h3>
                <div className="flex flex-wrap gap-3">
                  <div className="border border-[#7A7A7A] rounded-full px-4 py-2 text-sm flex items-center text-[#7A7A7A]">
                    <span className="mr-2"><FaBucket /></span> Living Areas & Bedrooms
                  </div>
                  <div className="border border-[#7A7A7A] rounded-full px-4 py-2 text-sm flex items-center text-[#7A7A7A]">
                    <span className="mr-2"><FaBucket /></span> Kitchen
                  </div>
                  <div className="border border-[#7A7A7A] rounded-full px-4 py-2 text-sm flex items-center text-[#7A7A7A]">
                    <span className="mr-2"><FaBucket /></span> Bathrooms
                  </div>
                  <div className="border border-[#7A7A7A] rounded-full px-4 py-2 text-sm flex items-center text-[#7A7A7A]">
                    <span className="mr-2"><FaBucket /></span> Floors
                  </div>
                </div>
              </div>

              {/* Includes & Excludes */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-6">
                <div>
                  <h3 className="text-md font-semibold mb-3">Includes</h3>
                  <ul className="space-y-2">
                    <li className="flex items-start">
                      {/* <span className="text-green-500 mr-2 mt-1"><FiCheck /></span> */}
                      <img src={Images.Check} className="scale-90 mr-2" alt="check" />
                      <span className="text-sm">1 carpet & a 5-seater sofa cleaning</span>
                    </li>
                    <li className="flex items-start">
                      <img src={Images.Check} className="scale-90 mr-2" alt="check" />
                      <span className="text-sm">Dry vacuuming of & curtains</span>
                    </li>
                    <li className="flex items-start">
                      <img src={Images.Check} className="scale-90 mr-2" alt="check" />
                      <span className="text-sm">2 kitchen cabinets cleaning</span>
                    </li>
                    <li className="flex items-start">
                      <img src={Images.Check} className="scale-90 mr-2" alt="check" />
                      <span className="text-sm">1 balcony & 1 bathroom cleaning</span>
                    </li>
                  </ul>
                </div>
                <div>
                  <h3 className="text-md font-semibold mb-3">Excludes</h3>
                  <ul className="space-y-2">
                    <li className="flex items-start">
                      <img src={Images.Cancel} className="scale-90 mr-2" alt="check" />
                      <span className="text-sm">Emptying & Cleaning of cupboards interior</span>
                    </li>
                    <li className="flex items-start">
                      <img src={Images.Cancel} className="scale-90 mr-2" alt="check" />
                      <span className="text-sm">Glue & paint stain removal</span>
                    </li>
                    <li className="flex items-start">
                      <img src={Images.Cancel} className="scale-90 mr-2" alt="check" />
                      <span className="text-sm">Terrace & inaccessible areas cleaning</span>
                    </li>
                    <li className="flex items-start">
                      <img src={Images.Cancel} className="scale-90 mr-2" alt="check" />
                      <span className="text-sm">Wet wiping of walls or ceiling</span>
                    </li>
                  </ul>
                </div>
              </div>

              {/* What we need from you */}
              <div className="mb-6">
                <h3 className="text-md font-semibold mb-3">What we need from you</h3>
                <div className="flex flex-wrap gap-3">
                  <div className="rounded-full px-4 py-2 text-sm flex items-center border border-[#7A7A7A] text-[#7A7A7A]">
                    <span className="mr-2"><FaBucket /></span> Cold Water Bucket
                  </div>
                  <div className="rounded-full px-4 py-2 text-sm flex items-center border border-[#7A7A7A] text-[#7A7A7A]">
                    <span className="mr-2"><FaBucket /></span> Hot Water Bucket
                  </div>
                  <div className=" rounded-full px-4 py-2 text-sm flex items-center border border-[#7A7A7A] text-[#7A7A7A]">
                    <span className="mr-2"><FaBucket /></span> Plug Point
                  </div>
                </div>
              </div>

              {/* Types of Cleaning Services */}
              <div>
                <h3 className="text-md font-semibold mb-3">Types Of Cleaning Services</h3>
                {/* Add content for types of cleaning services here */}
              </div>
            </div>
          )}

          {/* Ratings & Reviews Content */}
          {activeTab === 'ratings' && (
            <div className="py-4">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-lg font-semibold">All Reviews <span className="text-gray-500 font-normal text-sm">(451)</span></h2>
                <div className="flex items-center gap-4 ">
                  <div className='bg-[#F0F0F0] rounded-full p-2 '>
                       <GiSettingsKnobs />
                  </div>
                  <div className="relative ">
                    <select className="appearance-none border border-gray-300 bg-[#F0F0F0] rounded-full px-6 py-2  text-sm font-medium">
                      <option>Latest</option>
                      <option>Highest Rating</option>
                      <option>Lowest Rating</option>
                    </select>
                    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                      <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                        <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/>
                      </svg>
                    </div>
                  </div>
                  <button className="bg-black text-white rounded-full px-4 py-2 text-sm font-medium">
                    Write a Review
                  </button>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {rating.slice(0, visibleReviews).map((review) => (
                  <div key={review.id} className="bg-white border border-[#F0F0F0] rounded-lg p-4 mb-4">
                    <div className="flex justify-between items-start mb-2 h-[30%]">
                      <div>
                        <div className="text-yellow-400 mb-1">
                          {Array.from({ length: 5 }).map((_, index) => (
                            <span key={index} className="text-lg">
                              {index < review.rating ? '★' : '☆'}
                            </span>
                          ))}
                        </div>
                        <h3 className="font-medium">{review.name}</h3>
                      </div>
                      <button className="text-gray-400">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z" />
                        </svg>
                      </button>
                    </div>
                    <div className='flex flex-col justify-between h-[70%]'>

                    <p className="text-sm text-gray-600 mb-4 ">
                      {review.comment}
                    </p>
                    
                    <div className="flex justify-between  items-center text-xs text-gray-500">
                      <span>Posted on {review.date}</span>
                      <span>For {review.service}</span>
                    </div>
                    </div>
                  </div>
                ))}
                
                {visibleReviews < rating.length && (
                  <div className="md:col-span-2 flex justify-center mt-6">
                    <button 
                      className="text-black bg-white border border-[#F0F0F0] rounded-full px-6 py-2 text-sm font-medium hover:bg-gray-50"
                      onClick={() => setVisibleReviews(prevCount => Math.min(prevCount + 2, rating.length))}
                    >
                      Load More Reviews
                    </button>
                  </div>
                )}
              </div>
              
            </div>
          )}
        </div>

        {/* Right Content - Form (1/3 width on desktop) */}
        <div className="w-full lg:w-1/3">
          <div className="bg-[#FFF9CE] p-6 rounded-lg">
            <form onSubmit={handleSubmit}>
              {/* Name Field */}
              <div className="mb-4">
                <label htmlFor="name" className="block text-sm font-medium text-black mb-1">
                  Name
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="Enter your name"
                  className="w-full px-3 py-2 border border-gray-300 rounded-full bg-white "
                  required
                />
              </div>

              {/* Mobile Number Field */}
              <div className="mb-4">
                <label htmlFor="mobile" className="block text-sm font-medium text-black mb-1">
                  Mobile Number
                </label>
                <div className="flex">
                  {/* <span className="inline-flex items-center px-3 text-sm text-gray-900 bg-gray-200 border border-r-0 border-gray-300 rounded-l-lg">
                +91
              </span> */}
                  <input
                    type="tel"
                    id="mobile"
                    name="mobile"
                    value={formData.mobile}
                    onChange={handleInputChange}
                    placeholder="Enter your Mobile Number"
                    className="w-full px-3 py-2 border border-gray-300 rounded-full bg-white "
                    required
                  />
                </div>
              </div>

              {/* Service Name Field */}
              <div className="mb-4">
                <label htmlFor="serviceName" className="block text-sm font-medium text-black mb-1">
                  Service Name
                </label>
                <input
                  type="text"
                  id="serviceName"
                  name="serviceName"
                  value={formData.serviceName}
                  onChange={handleInputChange}
                  placeholder="Service name"
                  className="w-full px-3 py-2 border border-gray-300 rounded-full bg-white  "
                  required
                />
              </div>

              {/* Location Field */}
              <div className="mb-4">
                <label htmlFor="location" className="block text-sm font-medium text-black mb-1">
                  Location
                </label>
                <input
                  type="text"
                  id="location"
                  name="location"
                  value={formData.location}
                  onChange={handleInputChange}
                  placeholder="location"
                  className="w-full px-3 py-2 border border-gray-300 rounded-full bg-white  "
                  required
                />
              </div>

              {/* WhatsApp Updates Checkbox */}
              <div className="mb-6">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="getUpdates"
                    name="getUpdates"
                    checked={formData.getUpdates}
                    onChange={handleInputChange}
                    className="w-4 h-4  border-gray-300 rounded-full "
                  />
                  <label htmlFor="getUpdates" className="ml-2 text-sm text-black italic flex gap-2">
                    Get updates over WhatsApp <img src={Images.WhatsApp} alt="whatsapp" />
                  </label>
                </div>
              </div>

              {/* Submit Button */}
              <div className='w-full flex justify-end'>

                <button
                  type="submit"
                  className="w-[50%] bg-[#222222] text-white py-2 rounded-full font-medium hover:bg-gray-800 transition-colors"
                >
                  Schedule a Visit
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      {activeTab === 'details' && (
        <div>
          {/* Service Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow-md flex flex-col gap-0 border border-[#7A7A7A]">
              <div className="h-40 bg-[#F3F8FF] rounded-t-lg flex flex-col justify-start p-4 border-b border-[#7A7A7A]">
                <h2 className="text-xl font-semibold mb-2">Deep Home Cleaning</h2>
                <p className="text-[#7A7A7A] text-[15px] mb-6">
                 Deep cleaning is an intensive and thorough cleaning process designed to tackle areas and surfaces that aren't typically addressed during routine or daily cleaning tasks. 
                </p>
              </div>
              <div className="p-4 text-[15px]">
                <h3 className="text-[#535353] font-semibold mb-2">What you'll get</h3>
                <p className="text-[#7A7A7A] mb-3">Cleaning behind furniture/appliances <br/>✔ Thoroughly cleaned.</p>
                <p className="text-[#7A7A7A] mb-3">Baseboards, doors, and light fixtures <br/>✔ Wiped and sanitized.</p>
                <p className="text-[#7A7A7A] mb-3">✔ Includes ceilings, vents, and corners.</p>
                <p className="text-[#7A7A7A] mb-3">✔ Deep cleaning of oven, microwave (interior/exterior), and range hood.</p>
                <p className="text-[#7A7A7A] mb-3">Inside cabinets and drawers <br/> ✔ Fully cleaned.</p>
              </div>
                <div className="flex justify-between items-center px-4 py-3 border-t border-[#7A7A7A]">
                  <span className="font-bold text-lg">₹4,499.00</span>
                  <button 
                    className="bg-[#222222] text-white px-4 py-1 rounded-full text-sm"
                    onClick={() => handleBookNow('Deep Home Cleaning')}
                  >
                    Book Now
                  </button>
                </div>
            </div>


            <div className="bg-white rounded-lg shadow-md flex flex-col gap-0 border border-[#7A7A7A]">
              <div className="h-40 bg-[#FFF9CA] rounded-t-lg flex flex-col justify-start p-4 border-b border-[#7A7A7A]">
                <h2 className="text-xl font-semibold mb-2">Comprehensive Cleaning</h2>
                <p className="text-[#7A7A7A] text-[15px] mb-6">
                  Comprehensive cleaning refers to a thorough, detailed cleaning process that goes beyond basic surface cleaning to ensure every area of a space is properly cleaned and sanitized.                 </p>
              </div>
              <div className="p-4 text-[15px]">
                <h3 className="text-[#535353] font-semibold mb-2">What you'll get</h3>
                <p className="text-[#7A7A7A] mb-3">Baseboards, doors, and light fixtures  <br/>✔ Light wiping.</p>
                <p className="text-[#7A7A7A] mb-3">✔ Dusting of accessible surfaces.</p>
                <p className="text-[#7A7A7A] mb-3">✔ Exterior cleaning of appliances.</p>
                <p className="text-[#7A7A7A] mb-3">Cleaning behind furniture/appliances <br/> ✖ Not included.</p>
                <p className="text-[#7A7A7A] mb-3">Inside cabinets and drawers <br/> ✖ Not included.</p>
              </div>
                <div className="flex justify-between items-center px-4 py-3 border-t border-[#7A7A7A]">
                  <span className="font-bold text-lg">₹3,999.00</span>
                  <button 
                    className="bg-[#222222] text-white px-4 py-1 rounded-full text-sm"
                    onClick={() => handleBookNow('Comprehensive Cleaning')}
                  >
                    Book Now
                  </button>
                </div>
            </div>


            <div className="bg-white rounded-lg shadow-md flex flex-col gap-0 border border-[#7A7A7A]">
              <div className="h-40 bg-[#F3F8FF] rounded-t-lg flex flex-col justify-start p-4 border-b border-[#7A7A7A]">
                <h2 className="text-xl font-semibold mb-2">Regulars Cleaning </h2>
                <p className="text-[#7A7A7A] text-[15px] mb-6">
                    Regular cleaning involves routine tasks performed consistently to maintain cleanliness and organization in a space. It includes activities like dusting, vacuuming, wiping down surfaces.                </p>
              </div>
              <div className="p-4 text-[15px] ">
                <h3 className="text-[#535353] font-semibold mb-2">What you'll get</h3>
                <p className="text-[#7A7A7A] mb-3">✔ Basic surface dusting only.</p>
                <p className="text-[#7A7A7A] mb-3">Baseboards, doors, and light fixtures <br/>✖ Not included.</p>
                <p className="text-[#7A7A7A] mb-3">✖ Wiping exterior surfaces only.</p>
                <p className="text-[#7A7A7A] mb-3">Cleaning behind furniture/appliances <br/>✖ Not included.</p>
                <p className="text-[#7A7A7A] mb-3">Inside cabinets and drawers <br/> ✖ Not included. </p>
              </div>
                <div className="flex justify-between items-center px-4 py-3 border-t border-[#7A7A7A]">
                  <span className="font-bold text-lg">₹4,499.00</span>
                  <button 
                    className="bg-[#222222] text-white px-4 py-1 rounded-full text-sm"
                    onClick={() => handleBookNow('Regular Cleaning')}
                  >
                    Book Now
                  </button>
                </div>
            </div>

          </div>
          
        </div>
      )}


    </section>
  );
};

export default ServiceDetails;
