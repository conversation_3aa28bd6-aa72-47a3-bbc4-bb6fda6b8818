import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Images from '../assets/images';

const Login = () => {
  const [isLogin, setIsLogin] = useState(true);
  const [step, setStep] = useState(1); // Step 1: Mobile, Step 2: OTP
  const [mobileNumber, setMobileNumber] = useState('');
  const [otp, setOtp] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');

  const navigate = useNavigate();

  const handleMobileChange = (e) => {
    const value = e.target.value.replace(/\D/g, '').slice(0, 10);
    setMobileNumber(value);
  };

  const handleOtpChange = (e) => {
    const value = e.target.value.replace(/\D/g, '').slice(0, 6);
    setOtp(value);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (isLogin) {
      if (step === 1) {
        // Simulate sending OTP
        console.log('Send OTP to:', mobileNumber);
        setStep(2);
      } else {
        console.log('Login with OTP:', otp);
        navigate('/');
      }
    } else {
      console.log('Register with:', { firstName, lastName, email, mobileNumber });
      // Simulate registration then navigate
      navigate('/');
    }
  };

  return (
    <div className="flex flex-col xl:flex-row h-screen">
      {/* Left Image */}
      <div className="block xl:w-1/2 rounded-3xl p-10">
        <img
          src="https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80"
          alt="Service professionals"
          className="w-full h-full object-cover rounded-3xl"
        />
      </div>

      {/* Right Form */}
      <div className="w-full xl:w-1/2 flex flex-col items-center justify-start  p-8">
        <div className="w-full max-w-md">
          {/* Logo */}
          <div className="flex justify-center mb-2">
            <img
              src={Images.BusyBucketLogo || "/logo.png"}
              alt="Busy Bucket"
              className="h-20 w-20"
            />
          </div>

          <h2 className="text-center text-lg font-medium mb-4">Welcome to Busy Bucket</h2>

          {/* Toggle Buttons */}
          <div className='w-full flex justify-center items-center'>
            <div className="flex bg-[#FFF39F] rounded-full p-1 mb-4 w-[60%]">
              <button
                className={`w-1/2 py-2 rounded-full text-sm font-medium transition-colors ${isLogin ? 'bg-[#FEE942] text-black' : 'text-gray-600'}`}
                onClick={() => { setIsLogin(true); setStep(1); }}
              >
                Login
              </button>
              <button
                className={`w-1/2 py-2 rounded-full text-sm font-medium transition-colors ${!isLogin ? 'bg-[#FEE942] text-black' : 'text-gray-600'}`}
                onClick={() => setIsLogin(false)}
              >
                Register
              </button>
            </div>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit}>
            {!isLogin ? (
              <>
                {/* First Name */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                  <input
                    type="text"
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    className="w-full px-4 py-3 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-yellow-400"
                    placeholder="Enter your first name"
                    required
                  />
                </div>

                {/* Last Name */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                  <input
                    type="text"
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    className="w-full px-4 py-3 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-yellow-400"
                    placeholder="Enter your last name"
                    required
                  />
                </div>

                {/* Email */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full px-4 py-3 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-yellow-400"
                    placeholder="Enter your email address"
                    required
                  />
                </div>

                {/* Mobile Number */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Mobile Number</label>
                  <input
                    type="tel"
                    value={mobileNumber}
                    onChange={handleMobileChange}
                    className="w-full px-4 py-3 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-yellow-400"
                    placeholder="Enter your mobile number"
                    required
                  />
                </div>
              </>
            ) : (
              <>
                {step === 1 ? (
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Mobile Number</label>
                    <input
                      type="tel"
                      value={mobileNumber}
                      onChange={handleMobileChange}
                      className="w-full px-4 py-3 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-yellow-400"
                      placeholder="Enter your mobile number"
                      required
                    />
                  </div>
                ) : (
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Enter 6 Digit OTP Number</label>
                    <input
                      type="text"
                      value={otp}
                      onChange={handleOtpChange}
                      className="w-full px-4 py-3 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-yellow-400"
                      placeholder="Enter OTP Number"
                      required
                    />
                  </div>
                )}
              </>
            )}

            {/* Submit Button */}
            <div className="flex justify-end">
              <button
                type="submit"
                className="w-1/2 bg-black text-white py-3 rounded-full font-medium hover:bg-gray-800 transition-colors"
              >
                {isLogin ? (step === 1 ? 'Send OTP' : 'Login') : 'Register'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Login;
