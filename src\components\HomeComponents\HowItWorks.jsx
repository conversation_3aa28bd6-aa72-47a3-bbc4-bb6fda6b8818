import React from 'react';
import { FiShoppingBag, FiCalendar, FiCheckCircle } from 'react-icons/fi';
import ThreeImagesGrid from '../GlobalComponents/ThreeImagesGrid';
import Images from '../../assets/images';

const HowItWorks = () => {
  const steps = [
    {
      img : Images.ChooseYourService,
      title: "Choose Your Service",
      description: "Browse our offerings and select the one that fits your needs.",
    },
    {
      img : Images.ScheduleYourAppointment,
      title: "Schedule Your Appointment",
      description: "Pick a date and time that works best for you—it's quick and easy.",
    },
    {
      img : Images.GetItDone,
      title: "Get It Done",
      description: "Sit back and relax while we take care of everything for you!",
    },
  ];

  return (
    <section className=" sm:w-[85vw]">
      <div className="w-full">
        <div className="flex flex-col lg:flex-row items-center gap-8 ">
          
          {/* Left Content */}
          <div className="w-full lg:w-1/2">
            <div className="max-w-lg">
              <p className="text-gray-600 font-medium mb-2">How It Works</p>
              <h2 className="text-3xl md:text-[35px] font-bold mb-8">
                Follow Our Simple, Three-Step Process To Get Started:
              </h2>
              <div className="space-y-8">
                {steps.map((step, index) => (
                  <div key={index} className="flex items-start gap-4">
                    <div className="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center flex-shrink-0">
                     <img src={step.img} alt="" />
                    </div>
                    <div className="sm:w-[50%]">
                      <h3 className="text-xl font-semibold mb-1">{step.title}</h3>
                      <p className="text-gray-600">{step.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Content - Using ThreeImagesGrid */}
          <div className="w-full lg:w-1/2">
            <ThreeImagesGrid />
          </div>

        </div>
      </div>
    </section>
  );
};

export default HowItWorks;
