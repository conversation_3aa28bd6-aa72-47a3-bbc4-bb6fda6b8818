const ThreeImagesGrid = () => {
  return (
    <div className="flex sm:h-[500px] h-[380px] gap-4">
      {/* Left Part */}
      <div className="w-1/2 relative">
        <img
          src="https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60"
          alt="left"
          className="absolute bottom-0 h-[90%] w-full object-cover rounded-2xl"
        />
      </div>

      {/* Right Part */}
      <div className="w-1/2 flex flex-col gap-4">
        <img
          src="https://media.istockphoto.com/id/1049775258/photo/smiling-handsome-electrician-repairing-electrical-box-with-pliers-in-corridor-and-looking-at.jpg?s=612x612&w=0&k=20&c=stdWozouV2XsrHk2xXD3C31nT90BG7ydZvcpAn1Fx7I="
          alt="top-right"
          className="h-[60%] w-full object-cover rounded-2xl"
        />
        <img
          src="https://content.jdmagicbox.com/comp/indore/b9/0731px731.x731.170610182342.x7b9/catalogue/shekhar-car-painter-old-palasia-indore-wood-workers-2n111qp.jpg"
          alt="bottom-right"
          className="h-[30%] w-full object-cover rounded-2xl"
        />
      </div>
    </div>
  );
};

export default ThreeImagesGrid;
