import React from 'react';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Autoplay } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import { FaArrowLeft } from "react-icons/fa6";
import {FaArrowRight} from "react-icons/fa6";

const HappyClients = () => {
  // Sample testimonial data
  const testimonials = [
    {
      id: 1,
      name: '<PERSON>',
      rating: 5,
      verified: true,
      text: 'A well mannered, dedicated professional who is proficient in his work. He completed the work in time and to our full satisfaction. Thanks!'
    },
    {
      id: 2,
      name: '<PERSON>',
      rating: 5,
      verified: true,
      text: 'Majority of work done well but the minute details were missing. I appreciate that he cleaned everything when highlighted. Over all very good cleaning. Should focus on minute details...'
    },
    {
      id: 3,
      name: '<PERSON>',
      rating: 5,
      verified: true,
      text: 'As someone who\'s always on the lookout for unique fashion pieces, I\'m thrilled to have stumbled upon Shop.co. The selection of clothes is not only diverse but also on-point with the latest trends.'
    },
    {
      id: 4,
      name: '<PERSON>',
      rating: 5,
      verified: true,
      text: 'The service was exceptional! Everything was cleaned thoroughly and they paid attention to every detail. Will definitely book again.'
    },
    {
      id: 5,
      name: 'Jane S.',
      rating: 5,
      verified: true,
      text: 'The service was exceptional! Everything was cleaned thoroughly and they paid attention to every detail. Will definitely book again.'
    },
    {
      id: 6,
      name: 'Jane S.',
      rating: 5,
      verified: true,
      text: 'The service was exceptional! Everything was cleaned thoroughly and they paid attention to every detail. Will definitely book again.'
    },
    {
      id: 7,
      name: 'Jane S.',
      rating: 5,
      verified: true,
      text: 'The service was exceptional! Everything was cleaned thoroughly and they paid attention to every detail. Will definitely book again.'
    },
    {
      id: 8,
      name: 'Jane S.',
      rating: 5,
      verified: true,
      text: 'The service was exceptional! Everything was cleaned thoroughly and they paid attention to every detail. Will definitely book again.'
    },
  ];

  // Render stars based on rating
  const renderStars = (rating) => {
    return Array.from({ length: 5 }).map((_, index) => (
      <span key={index} className="text-yellow-400 text-xl">
        {index < rating ? '★' : '☆'}
      </span>
    ));
  };

  return (
    <section className=" px-4 w-[100vw]">
      <div className=" flex flex-col justify-center items-center">
        <div className="flex justify-between items-center sm:mb-20 mb-6 w-[80vw] ">
          <h2 className="text-3xl md:text-4xl font-bold">
            Hear from Our Happy Clients
          </h2>
          
          {/* Navigation Buttons - Positioned at top */}
          <div className="flex space-x-5">
            <button 
              className="happy-clients-prev  "
              aria-label="Previous testimonial"
            >
              <FaArrowLeft className="text-gray-700" size={25}/>
            </button>
            <button 
              className="happy-clients-next "
              aria-label="Next testimonial"
            >
              <FaArrowRight className="text-gray-700" size={25} />
            </button>
          </div>
        </div>

        {/* Testimonial Carousel Container with Blur Effect */}
        <div className="relative w-[95vw]">
          {/* Left Blur Effect */}
          <div className="absolute left-0 top-0 h-full w-16 bg-gradient-to-r from-white to-transparent z-10"></div>

          {/* Swiper Carousel */}
      <Swiper
  modules={[Navigation, Autoplay]}
  spaceBetween={24}
  slidesPerView="auto"
  centeredSlides={true}
  loop={true}
  autoplay={{
    delay: 5000,
    disableOnInteraction: false,
  }}
  navigation={{
    prevEl: '.happy-clients-prev',
    nextEl: '.happy-clients-next',
  }}
  className="testimonials-swiper py-4"
>
  {testimonials.map((testimonial) => (
    <SwiperSlide key={testimonial.id} className="!w-[380px]">
                <div className="bg-white rounded-lg p-6 shadow-md border border-gray-100 sm:h-[28vh] ">
                  {/* Stars */}
                  <div className="flex mb-1">
                    {renderStars(testimonial.rating)}
                  </div>

                  {/* Name and Verified Badge */}
                  <div className="flex items-center mb-2">
                    <h3 className="font-semibold text-lg">{testimonial.name}</h3>
                    {testimonial.verified && (
                      <span className="ml-2 bg-green-500 rounded-full w-4 h-4 flex items-center justify-center">
                        <span className="text-white text-xs">✓</span>
                      </span>
                    )}
                  </div>

                  {/* Testimonial Text */}
                  <p className="text-gray-600 text-sm">
                    "{testimonial.text}"
                  </p>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>

          {/* Right Blur Effect */}
          <div className="absolute right-0 top-0 h-full w-16 bg-gradient-to-l from-white to-transparent z-10"></div>
        </div>
      </div>
    </section>
  );
};

export default HappyClients;
