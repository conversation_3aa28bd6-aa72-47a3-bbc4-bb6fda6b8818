import React from 'react'
import Images from '../../assets/images';
import { Link } from 'react-router-dom';

const Thankyou = () => {
  return (
    <div className="flex flex-col items-center justify-center py-12 px-4 mx-auto text-center">
      {/* Thank you illustration */}
      <div className="mb-8">
        <img 
          src={Images.Thankyou} 
          alt="Thank you illustration" 
          className="w-64 h-64 object-contain"
        />
      </div>
      
      {/* Thank you message */}
      <div className="mb-8">
        <h1 className="text-[32px] font-bold mb-4 flex items-center  justify-center">
          <span className="text-blue-500 mr-2">▶</span>
          Thank you for being part of our 10K Busy Bucket Family!
        </h1>
        
        <p className="text-gray-600 mb-6">
          Your payment has been successfully completed.<br />
          We truly appreciate your support and look forward to<br />
          serving you. If you have any questions or need<br />
          assistance, we're here to help!
        </p>
        
        <Link to="/dashboard">
          <button className="bg-black text-white py-2 px-8 rounded-full hover:bg-gray-800 transition-all">
            Dashboard
          </button>
        </Link>
      </div>
    </div>
  );
}

export default Thankyou
