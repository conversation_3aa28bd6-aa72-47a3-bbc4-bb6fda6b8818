import React from 'react'
import ServiceCard from '../GlobalComponents/ServiceCard'
import Images from '../../assets/images'
import ThreeImagesGrid from '../GlobalComponents/ThreeImagesGrid'
import StatsBoxes from '../GlobalComponents/StatsBoxes'
import { useNavigate } from 'react-router-dom'

const HeroSection = () => {
    const navigate = useNavigate()
    return (
        <section className='flex flex-col gap-25 lg:px-16  '>
            <div className='flex flex-col lg:flex-row  lg:gap-10'>
                <div className='flex flex-col gap-3 my-10 lg:w-[50%]'>
                    <div className='text-[32px] md:text-[42px] font-bold text-left '>
                    <h1 className="whitespace-nowrap">
                        Reliable <span className="bg-yellow-300 px-2">Home Services</span>
                    </h1>
                    <h2 className="">
                        Delivered at your Doorstep
                    </h2>
                    </div>
                        
                    <div className="grid grid-cols-2 gap-4 mt-8 sm:w-[80%]">
                        <ServiceCard className="cursor-pointer" img={Images.Cleaning} text="Deep Cleaning" path={'/deepcleaning'} />
                        <ServiceCard img={Images.Plumbing} className="cursor-pointer" text="Plumbing" />
                        <ServiceCard img={Images.Electrical} className="cursor-pointer" text="Electrical Repairs" />
                        <ServiceCard img={Images.Carpenter} className="cursor-pointer" text="Carpentry" />
                    </div>
                </div>
                <div className='lg:w-[50%]'>
                    <ThreeImagesGrid />
                </div>




            </div>

            <StatsBoxes />
        </section>

    )
}

export default HeroSection
