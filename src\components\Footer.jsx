import React, { useState } from 'react';
import { FiMapPin, FiPhone, FiMail } from 'react-icons/fi'; import { Link } from 'react-router-dom';
import { GoMail } from "react-icons/go";
import Images from '../assets/images';

function Footer() {
    const [email, setEmail] = useState('');
    const handleSubmit = (e) => {
        e.preventDefault();
        // Handle newsletter subscription    console.log('Subscribing email:', email);
        setEmail('');
    };
    return (
        <footer className="bg-black text-white pt-10 pb-6 relative sm:mt-[25vh] mt-[15vh] ">
            <div className="container mx-auto px-4 ">
                {/* Newsletter Section */}
                <div className="bg-yellow-300 w-[80%] text-black sm:py-8 sm:px-12 p-4 rounded-lg mb-10 flex lg:flex-row flex-col items-center justify-between absolute sm:-top-[20%] -top-[8%] left-1/2 -translate-x-1/2">
                    <div className="lg:text-[40px]">
                        <h2 className=" font-bold ">STAY UPTO DATE ABOUT</h2>
                        <h2 className=" font-bold ">OUR LATEST OFFERS</h2>
                    </div>
                    <form onSubmit={handleSubmit} className="">
                        <div className="flex flex-col gap-4">
                            <div className="flex items-center bg-white rounded-full sm:py-3 sm:px-16 py-2 px-4 mt-2">
                                <GoMail className="text-gray-500 mr-2" />
                                <input
                                    type="email"
                                    placeholder="Enter your email address"
                                    className="flex-grow outline-none text-black placeholder-gray-500 bg-transparent"
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                    required
                                />
                            </div>
                            <button type="submit" className="bg-black text-white sm:py-3 sm:px-16 py-2 px-4 rounded-full">
                                Subscribe to Newsletter
                            </button>
                        </div>
                    </form>        </div>
                {/* Main Footer Content */}
                <div className="grid grid-cols-1 md:grid-cols-4 sm:gap-8 gap-12 pt-20 ">          {/* Column 1: Company Info */}
                    <div>
                        <h2 className="text-[33px] font-bold mb-2">Busy Bucket</h2>
                        <p className="text-gray-300 mb-6">The aim is to build a one-stop-shop for all home services with affordable price and high quality with a professional methodology which we proudly call "the 360 degrees of happiness"
                        </p>
                        <div className="flex space-x-3">
                            <a href="#" className="w-8 h-8 rounded-full bg-white flex items-center justify-center">                
                                <img src={Images.Twitter} alt="" /> </a>
                            <a href="#" className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center">
                                <img src={Images.Facebook} alt="" /> </a>
                            <a href="#" className="w-8 h-8 rounded-full bg-white flex items-center justify-center">               
                                <img src={Images.Instagram} alt="" /> </a>
                            <a href="#" className="w-8 h-8 rounded-full bg-white flex items-center justify-center">
                                <img src={Images.Github} alt="" /> </a>
                        </div>
                    </div>
                    {/* Column 2: Company Links */}
                    <div>            <h3 className="text-lg font-semibold mb-4">COMPANY</h3>
                        <ul className="space-y-3">              <li><Link to="/" className="text-gray-300 hover:text-white">Home</Link></li>
                            <li><Link to="/services" className="text-gray-300 hover:text-white">Services</Link></li>              <li><Link to="/about" className="text-gray-300 hover:text-white">About Us</Link></li>
                            <li><Link to="/contact" className="text-gray-300 hover:text-white">Contact Us</Link></li>            </ul>
                    </div>
                    {/* Column 3: Help Links */}          <div>
                        <h3 className="text-lg font-semibold mb-4">HELP</h3>            <ul className="space-y-3">
                            <li><Link to="/support" className="text-gray-300 hover:text-white">Customer Support</Link></li>              <li><Link to="/refund" className="text-gray-300 hover:text-white">Refund Policy</Link></li>
                            <li><Link to="/terms" className="text-gray-300 hover:text-white">Terms & Conditions</Link></li>              <li><Link to="/privacy" className="text-gray-300 hover:text-white">Privacy Policy</Link></li>
                        </ul>          </div>
                    {/* Column 4: Contact Info */}
                    <div>            <h3 className="text-lg font-semibold mb-4">CONTACT INFO</h3>
                        <ul className="space-y-4">              <li className="flex items-start">
                            <FiMapPin className="mt-1 mr-2 flex-shrink-0" />                <span>F-298, 5th Floor, Sector 74, Sahibzada Ajit Singh Nagar, Punjab 160014</span>
                        </li>              <li className="flex items-start">
                                <FiMapPin className="mt-1 mr-2 flex-shrink-0" />                <span>Ganga Vihar, Lane No 2, Rajeev Nagar Dehradun, Uttarakhand 248001</span>
                            </li>              <li className="flex items-center">
                                <FiPhone className="mr-2 flex-shrink-0" />                <span>Call us: 08195220004</span>
                            </li>              <li className="flex items-center">
                                <FiMail className="mr-2 flex-shrink-0" />                <span>Email Us: <EMAIL></span>
                            </li>            </ul>
                    </div>        </div>
                {/* Copyright and Payment Methods */}
                <div className="mt-10 pt-6 border-t border-gray-800 flex flex-col md:flex-row justify-between items-center">          <p className="text-gray-400 mb-4 md:mb-0">Busy Bucket © 2024. All Rights Reserved</p>
                    <div className="flex space-x-3">
                        <img src={Images.Visa} alt="Visa" className="h-8" />
                        <img src={Images.Mastercard} alt="Mastercard" className="h-8" />
                        <img src={Images.Paypal} alt="PayPal" className="h-8" />
                        <img src={Images.ApplePay} alt="Apple Pay" className="h-8" />
                        <img src={Images.GooglePay} alt="Google Pay" className="h-8" />
                    </div>
                </div>
            </div>
        </footer>
    );
}

export default Footer;

































































