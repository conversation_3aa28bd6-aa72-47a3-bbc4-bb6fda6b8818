import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { FiUser, FiGift, FiShare2, FiCalendar, FiLogOut } from 'react-icons/fi';
import { RiArrowRightSLine } from "react-icons/ri";

const Sidebar = () => {
  const location = useLocation();
  const path = location.pathname;

  const menuItems = [
    {
      title: 'Personal Info',
      icon: <FiUser className="w-5 h-5" />,
      path: '/account',
      badge: '37% profile complete'
    },
    {
      title: 'My Coupons',
      icon: <FiGift className="w-5 h-5" />,
      path: '/account/coupons'
    },
    {
      title: 'Refer a Friend',
      icon: <FiShare2 className="w-5 h-5" />,
      path: '/account/refer'
    },
    {
      title: 'Bookings',
      icon: <FiCalendar className="w-5 h-5" />,
      path: '/account/bookings'
    },
    {
      title: 'Logout',
      icon: <FiLogOut className="w-5 h-5" />,
      path: '/logout'
    }
  ];

  const isActive = (itemPath) => {
    if (itemPath === '/account' && path === '/account') {
      return true;
    }
    return path.includes(itemPath) && itemPath !== '/account';
  };

  return (
    <div className="sm:w-64 bg-white sm:pt-16 mt-4 border-gray-200 min-h-screen sm:p-4 p-2">
      <div className="space-y-1">
        {menuItems.map((item, index) => (
          <Link
            key={index}
            to={item.path}
            className={`flex items-center sm:px-4 py-2 sm:gap-2 gap-1 rounded-lg transition-colors`}
          >
            <RiArrowRightSLine size={20} className={` ${isActive(item.path) ? '' : 'invisible'} `} />
            <span className='block sm:hidden'>{item.icon}</span>
            <span className="font-medium hidden sm:block">{item.title}</span>
          </Link>
        ))}

        {/* Logout Button */}
        {/* <button className="flex items-center w-full px-4 py-3 mt-4 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors">
          <FiLogOut className="w-5 h-5 mr-3 sm:hidden" />
          <span className="font-medium hidden sm:block">Logout</span>
        </button> */}
      </div>
    </div>
  );
};

export default Sidebar;
