import React, { useState } from 'react'
import { FiEdit2, FiX, FiPlus } from 'react-icons/fi';
import { FaPlusCircle } from "react-icons/fa";

const Address = () => {
     const [selectedAddress, setSelectedAddress] = useState('home');

      const addresses = [
        {
            id: 'home',
            label: 'Home',
            street: '2118 Thornridge Cir, Syracuse, Connecticut 35624',
            phone: '(*************'
        },
        {
            id: 'office',
            label: 'Office',
            street: '2715 Ash Dr, San Jose, South Dakota 83475',
            phone: '(*************'
        }
    ];

     const handleAddressSelect = (addressId) => {
        setSelectedAddress(addressId);
    };

  return (
     <div>
                    <h2 className="text-xl font-semibold mb-6">Select Address</h2>

                    <div className="space-y-4">
                        {addresses.map((address) => (
                            <div
                                key={address.id}
                                className={`p-4 rounded-lg border bg-[#F6F6F6] ${selectedAddress === address.id ? 'border-black ' : 'border-gray-200'}`}
                            >
                                <div className="flex items-start justify-between">
                                    <div className="flex items-start">
                                        <input
                                            type="radio"
                                            id={address.id}
                                            name="address"
                                            checked={selectedAddress === address.id}
                                            onChange={() => handleAddressSelect(address.id)}
                                            className="mt-1 mr-3"
                                        />
                                        <div>
                                            <div className="flex items-center">
                                                <label htmlFor={address.id} className="font-medium">{address.label}</label>
                                                {selectedAddress === address.id && (
                                                    <span className="ml-2 text-xs bg-black text-white px-2 py-0.5 rounded">
                                                        {address.id === 'home' ? 'HOME' : 'OFFICE'}
                                                    </span>
                                                )}
                                            </div>
                                            <p className="text-sm text-gray-600 mt-1">{address.street}</p>
                                            <p className="text-sm text-gray-600">{address.phone}</p>
                                        </div>
                                    </div>
                                    <div className="flex space-x-2">
                                        <button className="text-gray-500 hover:text-gray-700">
                                            <FiEdit2 size={18} />
                                        </button>
                                        <button className="text-gray-500 hover:text-gray-700">
                                            <FiX size={18} />
                                        </button>
                                    </div>
                                </div>
                            </div>
                        ))}

                        {/* Add New Address Button */}
                        <button className="w-full py-4 flex flex-col items-center justify-center text-black text-[14px] ">
                            <FaPlusCircle size={20} className="mr-2 text-black" />
                            Add New Address
                        </button>
                    </div>
                </div>
  )
}

export default Address
