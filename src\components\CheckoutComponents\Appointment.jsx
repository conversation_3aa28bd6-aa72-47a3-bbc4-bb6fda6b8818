import React, { useState } from 'react'

const Appointment = () => {
    const [selectedTime, setSelectedTime] = useState('06:00 AM');

    return (
        <div>
            <h2 className="text-xl font-semibold mb-6">Book a slot</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {/* Date Selection */}
                <div>
                    <h3 className="font-medium mb-4">Date</h3>
                    {/* <input type="date" name="date" id="" /> */}
                    <div className="relative">
                        <div className="border border-gray-200 rounded-lg p-4">
                            <div className="flex justify-between items-center mb-4">
                                <button className="text-gray-500">«</button>
                                <span className="font-medium">November 2022</span>
                                <button className="text-gray-500">»</button>
                            </div>

                            <div className="grid grid-cols-7 gap-1">
                                {/* Days of week */}
                                {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map((day, index) => (
                                    <div key={index} className="text-center text-xs font-medium text-gray-500 mb-2">
                                        {day}
                                    </div>
                                ))}

                                {/* Empty cells for previous month */}
                                {[...Array(2)].map((_, index) => (
                                    <div key={`empty-${index}`} className="text-center py-2 text-xs text-gray-300">
                                        {30 + index}
                                    </div>
                                ))}

                                {/* Days of current month */}
                                {Array.from({ length: 30 }, (_, i) => i + 1).map(date => (
                                    <div
                                        key={date}
                                        className={`text-center py-2 text-xs rounded-full cursor-pointer hover:bg-gray-100 ${date === 24 ? 'bg-black text-white' : ''
                                            }`}
                                    >
                                        {date}
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Time Slot Selection */}
                <div>
                    <h3 className="font-medium mb-4">Select start time of service</h3>
                    <div className="mb-4">
                        <div className="relative">
                            <select className="w-full p-3 border border-gray-200 rounded-lg appearance-none pr-10 bg-white">
                                <option>Morning</option>
                                <option>Afternoon</option>
                                <option>Evening</option>
                            </select>
                            <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                                <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div className="grid grid-cols-4 gap-2">
                        {[
                            ['06:00 AM', '07:00 AM', '08:00 AM', '09:00 AM'],
                            ['10:00 AM', '11:00 AM', '12:00 AM', '01:00 PM'],
                            ['02:00 PM', '03:00 PM', '04:00 PM', '05:00 PM'],
                            ['06:00 PM', '07:00 PM', '08:00 PM', '09:00 PM']
                        ].map((row, rowIndex) => (
                            <React.Fragment key={`row-${rowIndex}`}>
                                {row.map((time, index) => (
                                    <div
                                        onClick={() => setSelectedTime(time)}
                                        key={`${rowIndex}-${index}`}
                                        className={`text-center py-2 px-1 text-xs border rounded-md cursor-pointer ${selectedTime === time ? 'border-black bg-gray-50' : 'border-gray-200'
                                            }`}
                                    >
                                        {time}
                                    </div>
                                ))}
                            </React.Fragment>
                        ))}
                    </div>
                </div>
            </div>

            {/* Special Instructions */}
            <div className="mt-8">
                <h3 className="font-medium mb-2">Main Pain Area, Special Instruction For Cleaning (Optional)</h3>
                <textarea
                    className="w-full p-3 border border-gray-200 rounded-lg resize-none"
                    rows="3"
                    placeholder="Type Your Comment..."
                ></textarea>
                <div className="flex justify-end mt-1">
                    <span className="text-xs text-gray-500">0 / 500</span>
                </div>
            </div>
        </div>
    )
}

export default Appointment
