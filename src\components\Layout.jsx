import { styled } from 'styled-components';
import Navbar from './Navbar';
import Footer from './Footer';
import { Outlet } from 'react-router-dom';

const LayoutContainer = styled.div`
  min-height: 100vh;
  display: flex;
  flex-direction: column;
`;

const MainContent = styled.main`
  flex: 1;
  
`;

function Layout() {
  return (
    <LayoutContainer className="bg-[#ffffff] overflow-hidden">
      <Navbar />
      <MainContent className="container mx-auto">
        <Outlet /> {/* This renders the child route (like HomePage) */}
      </MainContent>
      <Footer />
    </LayoutContainer>
  );
}

export default Layout;
