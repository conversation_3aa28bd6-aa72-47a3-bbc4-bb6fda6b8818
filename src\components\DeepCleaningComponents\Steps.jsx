import React, { useState } from 'react';
import { IoMdClose } from 'react-icons/io';
import { FaArrowLeft } from 'react-icons/fa';
import ServiceCard from '../GlobalComponents/ServiceCard';
import Images from '../../assets/images';
import { useNavigate } from 'react-router-dom';

const Boxes = [
    {
        img: Images.Cleaning,
        text: "1 BHK Apartment Cleaning"
    },
    {
        img: Images.Cleaning,
        text: "2 BHK Apartment Cleaning"
    },
    {
        img: Images.Cleaning,
        text: "3 BHK Apartment Cleaning"
    },
    {
        img: Images.Cleaning,
        text: "4 BHK Apartment Cleaning"
    },
    {
        img: Images.Cleaning,
        text: "5 BHK Apartment Cleaning"
    },
];

const Steps = ({ onClose, service }) => {
    const [currentStep, setCurrentStep] = useState(1);
    const navigate = useNavigate()
    const handleNextStep = () => {
        setCurrentStep(prev => prev + 1);
    };

    const handlePrevStep = () => {
        setCurrentStep(prev => prev - 1);
    };

    return (
        <div className="fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50">
            <div className="bg-white rounded-lg w-full max-w-[750px] relative">
                {/* Close button */}
                <button
                    onClick={onClose}
                    className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
                >
                    <IoMdClose size={24} />
                </button>

                {/* Step 1: Select Property Type */}
                {currentStep === 1 && (
                    <div className="p-6 pb-12">
                        <h2 className="text-xl font-bold text-center mb-6">DEEP CLEANING</h2>

                        <div className="grid grid-cols-2 gap-4">
                            <div
                                className=" rounded-lg overflow-hidden p-3 cursor-pointer shadow-2xl transition-shadow"
                                onClick={handleNextStep}
                            >
                                <h3 className="font-medium mb-2">Flat</h3>
                                <img
                                    src="https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60"
                                    alt="Flat"
                                    className="w-full h-40 object-cover rounded-lg mb-2 "
                                />


                                <p className="text-sm text-gray-600">Deep Cleaning, Deeper Comfort – We Leave No Corner Untouched!</p>

                            </div>
                            <div
                                className=" rounded-lg overflow-hidden p-3 cursor-pointer shadow-2xl transition-shadow"
                                onClick={handleNextStep}
                            >
                                <h3 className="font-medium mb-2">Independent</h3>
                                <img
                                    src="https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60"
                                    alt="Independent"
                                    className="w-full h-40 object-cover rounded-lg mb-2 "
                                />


                                <p className="text-sm text-gray-600">Deep Cleaning, Deeper Comfort – We Leave No Corner Untouched!</p>

                            </div>
                        </div>
                    </div>
                )}

                {/* Step 2: Choose BHK Size */}
                {currentStep === 2 && (
                    <div className="p-6">
                        <div className="flex items-center mb-6">
                            <button
                                onClick={handlePrevStep}
                                className="mr-4 text-gray-600 hover:text-gray-800"
                            >
                                <FaArrowLeft size={16} />
                            </button>
                            <h2 className="text-xl font-bold text-center flex-1">CHOOSE YOUR BHK SIZE</h2>
                        </div>

                        <div className="grid grid-cols-3 gap-4 mb-4 ">
                            {Boxes.slice(0, 3).map((box, index) => (
                                <div key={index} onClick={handleNextStep} className="flex justify-between border-[2px] shadow-2xl gap-1 border-gray-400 p-2 rounded-lg  sm:h-16">
                                    <div className=" rounded-lg w-[20%]  ">
                                        <img className=' w-full ' src={box.img} alt={box.text} />
                                    </div>
                                    <div className="flex justify-start  items-center w-[80%]  ">
                                        <h3 className="font-semibold sm:text-[16px] text-[10px]">{box.text}</h3>
                                    </div>

                                </div>
                            ))}
                            {/* <ServiceCard img={Images.Cleaning} text="1 BHK" />
                <ServiceCard img={Images.Cleaning} text="2 BHK" />
                <ServiceCard img={Images.Cleaning} text="3 BHK" /> */}
                        </div>

                        <div className="flex justify-center  gap-4 ">
                            <div className='w-[60%] flex gap-4'>

                           {Boxes.slice(3, 5).map((box, index) => (
                                <div key={index} onClick={handleNextStep} className="flex justify-between border-[2px] shadow-2xl gap-1 border-gray-400 p-2 rounded-lg sm:h-16">
                                    <div className=" rounded-lg w-[20%]  ">
                                        <img className=' w-full ' src={box.img} alt={box.text} />
                                    </div>
                                    <div className="flex justify-start  items-center w-[80%]  ">
                                        <h3 className="font-semibold sm:text-[16px] text-[10px]">{box.text}</h3>
                                    </div>

                                </div>
                            ))}
                            </div>
                        </div>
                    </div>
                )}

                {/* Step 3: Choose Date and Time */}
                {currentStep === 3 && (
                    <div className="p-6 pb-12">
                        <h2 className="text-xl font-bold text-center mb-6">DEEP CLEANING</h2>

                        <div className="grid grid-cols-2 gap-4">
                            <div
                                className=" rounded-lg overflow-hidden p-3 cursor-pointer shadow-2xl transition-shadow"
                                onClick={handleNextStep}
                            >
                                <h3 className="font-medium mb-2">Flat</h3>
                                <img
                                    src="https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60"
                                    alt="Flat"
                                    className="w-full h-40 object-cover rounded-lg mb-2 "
                                />


                                <p className="text-sm text-gray-600">Deep Cleaning, Deeper Comfort – We Leave No Corner Untouched!</p>
                                <div className='flex justify-between items-center mt-4'>
                                     <span className="font-bold text-[15px]">₹4,499.00</span>
                                     <button onClick={() => navigate("/address")} className="bg-black text-white text-[14px] px-6 py-1 rounded-full font-medium">Book Now</button>
                                </div>

                            </div>
                            <div
                                className=" rounded-lg overflow-hidden p-3 cursor-pointer shadow-2xl transition-shadow"
                                onClick={handleNextStep}
                            >
                                <h3 className="font-medium mb-2">Independent</h3>
                                <img
                                    src="https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60"
                                    alt="Independent"
                                    className="w-full h-40 object-cover rounded-lg mb-2 "
                                />


                                <p className="text-sm text-gray-600">Deep Cleaning, Deeper Comfort – We Leave No Corner Untouched!</p>
                                  <div className='flex justify-between items-center mt-4'>
                                     <span className="font-bold text-[15px]">₹3,499.00</span>
                                     <button onClick={() => navigate("/address")} className="bg-black text-white text-[14px] px-6 py-1 rounded-full font-medium">Book Now</button>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default Steps



 {/* Time Slots */}
// <div>
//     <h3 className="font-medium mb-3">Select Time Slot</h3>
//     <div className="grid grid-cols-3 gap-3">
//         {['09:00 AM', '10:00 AM', '11:00 AM', '12:00 PM', '01:00 PM', '02:00 PM', '03:00 PM', '04:00 PM', '05:00 PM'].map((time, index) => (
//             <div
//                 key={index}
//                 className={`text-center py-2 border rounded-lg cursor-pointer hover:border-black ${time === '10:00 AM' ? 'border-black bg-gray-50' : 'border-gray-200'
//                     }`}
//             >
//                 {time}
//             </div>
//         ))}
//     </div>
// </div>