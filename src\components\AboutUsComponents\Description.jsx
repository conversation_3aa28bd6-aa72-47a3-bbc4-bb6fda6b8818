import React from 'react';

const Description = () => {
  return (
    <div className=" w-[80vw] mx-auto  px-4 py-10 space-y-24">
      {/* About Us Section */}
      <div className="flex flex-col md:flex-row items-center gap-6 ">
        <img
          src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTtfkD0tuTTP7aQWs30Wittg5ymrE1r-XuSwg&s"
          alt="About Us"
          className="rounded-2xl w-full md:w-[50%] object-cover"
        />
        <div className='md:w-1/2  md:px-16 '>
          <h2 className="text-xl font-semibold mb-2">About Us</h2>
          <p className="text-gray-600 text-sm">
            The journey of the Busy Bucket family started back in 2017 at Mohali. It all began when two soul partners
            needed home cleaning services but got disappointed to see a lack of professional service providers. <br />
            The duo has rich hospitality background experiences & one from an HR / Training field.
            <br />
            They started a small operation with a laptop, sofa to sit and a mobile phone and named it as <strong>Busy Bucket</strong>. 
            The aim is to build a one-stop-shop for all home services with affordable price and high quality.
          </p>
        </div>
      </div>

      {/* Experience Section */}
      <div className="flex flex-col-reverse md:flex-row items-center gap-6 bg-[#F9F9F9]  rounded-md min-h-[70vh]">
        <div className="flex w-full md:w-1/2 flex-col justify-center p-4 sm:px-16 sm:min-h-[70vh]">
          <h2 className="text-4xl font-semibold mb-3">Experience Excellence In Home Cleaning Services</h2>
          <p className="text-gray-600 text-sm mb-3">
            We have been doing this work since 2017. Our team is proper trained & experienced. You can book us online
            or call to make your place shine like never before. In case of event, if you're not satisfied with the work,
            we’ll do it again. Let us know, we would be very happy to help you.
          </p>
          <ul className="list-disc list-inside text-sm text-gray-700 space-y-1">
            <li>Experienced team</li>
            <li>Human friendly, non-corrosive</li>
            <li>Fully Mechanized Cleaning with No Harmful Chemical</li>
            <li>Customer Satisfaction Guaranteed</li>
            <li>ISO 9001:2015 Certified</li>
            <li>‘Do it again’ policy</li>
          </ul>
        </div>
        <img
          src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcThLpmRZ1nHmYqtATnHhWfIfQPf-_Km366kcA&s"
          alt="Cleaning"
          className="rounded-r-md w-full md:w-1/2 object-cover h-full md:min-h-[70vh] "
        />
      </div>
    </div>
  );
};

export default Description;
