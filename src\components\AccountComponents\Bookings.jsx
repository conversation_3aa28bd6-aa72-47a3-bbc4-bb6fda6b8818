import React, { useState } from "react";
import Images from "../../assets/images";
import { RxCross2 } from "react-icons/rx";

const dummyBookings = {
    past: [
        {
            id: 1,
            service: "Plastic Water Tank 1000 - 1500 Ltr",
            price: "₹5,306.82",
            paymentMode: "Gpay",
            date: "16th Dec 2024",
            status: "Completed",
            worker: "<PERSON><PERSON><PERSON>",
        },
        {
            id: 2,
            service: "Plastic Water Tank 1000 - 1500 Ltr",
            price: "₹5,306.82",
            paymentMode: "Gpay",
            date: "20th Aug 2024",
            status: "Cancelled",
            worker: "<PERSON><PERSON><PERSON>",
        },
        {
            id: 3,
            service: "Plastic Water Tank 1000 - 1500 Ltr",
            price: "₹5,306.82",
            paymentMode: "Gpay",
            date: "20th Aug 2024",
            status: "Completed",
            worker: "<PERSON><PERSON><PERSON>",
        },
        {
            id: 4,
            service: "Plastic Water Tank 1000 - 1500 Ltr",
            price: "₹5,306.82",
            paymentMode: "Gpay",
            date: "20th Aug 2024",
            status: "Completed",
            worker: "<PERSON><PERSON><PERSON>",
        },
    ],
    active: [
        {
            id: 5,
            service: "Plastic Water Tank 1000 - 1500 Ltr",
            price: "₹5,306.82",
            paymentMode: "Gpay",
            date: "25th Jun 2025",
            status: "Scheduled",
            worker: "Rakesh <PERSON>",
        },
    ],
};

const Bookings = () => {
    const [activeTab, setActiveTab] = useState("past");
    const [showCancelModal, setShowCancelModal] = useState(false);
    const [selectedReason, setSelectedReason] = useState("");
    const [showSuccess, setShowSuccess] = useState(false);

    const reasons = [
        "Something came up, and I won’t be home at the scheduled time",
        "I need to reschedule due to a change in my plans",
        "I accidentally booked the wrong date or time",
        "I found a cheaper alternative",
        "The event I needed cleaning for was canceled",
        "Others",
    ];

    const handleCancel = () => {
        if (selectedReason) {
            setShowCancelModal(false);
            setShowSuccess(true);
        }
    };

    const closeAll = () => {
        setShowSuccess(false);
        setSelectedReason("");
    };


    const bookings = activeTab === "past" ? dummyBookings.past : dummyBookings.active;

    return (

        <div>
            <h1 className="text-2xl font-medium mb-6 text-center">Bookings</h1>
            <div className="max-w-4xl mx-auto rounded-lg border-[1px] border-gray-400 p-4">
                <div className="mx-auto">
                    {/* <h1 className="text-2xl font-semibold mb-6 text-center">Bookings</h1> */}

                    {/* Tabs */}
                    <div className="flex border-b border-gray-400  mb-6 ">
                        <button
                            onClick={() => setActiveTab("past")}
                            className={`px-4 py-2 text-sm font-medium ${activeTab === "past"
                                ? "border-b-2 border-black text-black"
                                : "text-gray-500"
                                }`}
                        >
                            Past Booking History
                        </button>
                        <button
                            onClick={() => setActiveTab("active")}
                            className={`px-4 py-2 text-sm font-medium ml-4 ${activeTab === "active"
                                ? "border-b-2 border-black text-black"
                                : "text-gray-500"
                                }`}
                        >
                            Active & Upcoming Booking
                        </button>
                    </div>

                    {/* Booking Cards */}
                    <div className="space-y-4">
                        {bookings.map((item) => (
                            <div
                                key={item.id}
                                className=" bg-[#F6F6F6] rounded-md p-2 px-4 flex flex-col sm:flex-row sm:items-start justify-between shadow-sm"
                            >
                                {/* Left - Image + Button */}
                                <div className="flex items-start ">

                                    <div className="flex flex-col items-start gap-2 mb-4 sm:mb-0">
                                        <div className="flex ml-4">

                                            <img
                                                src={Images.VaccumCleaner}
                                                alt="service"
                                                className="w-14 h-14 rounded-md object-cover"
                                            />
                                            <div className="flex flex-col space-y-1 text-sm text-black ml-4  mt-3">
                                                <p><span className="text-[#777777]" >Service:</span> {item.service}</p>
                                                <p><span className="text-[#777777]">Payment Mode:</span> {item.paymentMode}</p>
                                                {/* <p><strong>Service By:</strong> {item.worker}</p> */}
                                            </div>
                                        </div>
                                        {activeTab === "past" ? (
                                            <button className="px-4 py-1 bg-black rounded-full text-white text-sm">
                                                Book Again
                                            </button>
                                        ) : (
                                            <div className="space-x-3" >

                                                <button
                                                    onClick={() => setShowCancelModal(true)}
                                                    className="px-6 py-1 bg-black rounded-full text-white text-sm">
                                                    Cancel
                                                </button>
                                                <button className="px-6 py-1 bg-black rounded-full text-white text-sm">
                                                    Review
                                                </button>
                                            </div>
                                        )}
                                    </div>

                                    {/* Middle - Info */}

                                </div>

                                <div className="flex flex-col space-y-1  text-sm text-black mt-3">
                                    <p><span className="text-[#777777]">Price:</span> {item.price}</p>
                                    <p><span className="text-[#777777]">Service By:</span> {item.worker}</p>
                                </div>

                                {/* Right - Price/Date/Status */}
                                <div className="space-y-1 text-left text-sm min-w-[150px] mt-3  ">
                                    {/* <p><strong>Price:</strong> {item.price}</p> */}
                                    <p><span className="text-[#777777]">Date:</span> {item.date}</p>
                                    <p>
                                        <span className="text-[#777777]">Status:</span>{" "}
                                        <span
                                            className={`${item.status === "Completed"
                                                ? "text-green-600"
                                                : item.status === "Cancelled"
                                                    ? "text-red-500"
                                                    : "text-blue-500"
                                                } font-medium`}
                                        >
                                            {item.status}
                                        </span>
                                    </p>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            {/* Cancel Modal */}
            {showCancelModal && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-opacity-50 backdrop-blur-xs">
                    <div className="bg-white max-w-lg w-full p-6 rounded-lg relative">
                        <button
                            onClick={() => setShowCancelModal(false)}
                            className="absolute top-2 right-3 bg-black text-white hover:bg-gray-800 rounded-full w-9 h-9 flex items-center justify-center text-lg"
                        >
                            {/* &times; */}
                            <RxCross2 className="" />
                        </button>

                        <h2 className="text-center text-lg font-semibold mb-2">
                            CANCEL BOOKING
                        </h2>
                        <p className="text-center text-sm mb-4">
                            Select Reason For Cancellation
                        </p>

                        <div className="space-y-3">
                            {reasons.map((reason, idx) => (
                                <label
                                    key={idx}
                                    className="flex items-start gap-2 cursor-pointer border-b-[1px] border-gray-400 pb-3"
                                >
                                    <input
                                        type="radio"
                                        name="reason"
                                        value={reason}
                                        checked={selectedReason === reason}
                                        onChange={(e) => setSelectedReason(e.target.value)}
                                        className="mt-1"
                                    />
                                    <span className="text-sm text-gray-700">{reason}</span>
                                </label>
                            ))}
                        </div>

                        <div className="mt-6 text-center">
                            <button
                                onClick={handleCancel}
                                className="bg-black text-white px-6 py-2 rounded-full hover:bg-gray-800 transition cursor-pointer"
                            >
                                Confirm Cancellation
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {/* Success Modal */}
            {showSuccess && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-opacity-40 backdrop-blur-sm">
                    <div className="bg-white max-w-lg w-[90%] p-6 sm:p-10 rounded-lg text-center relative">
                        {/* Close Icon */}
                        <button
                            onClick={closeAll}
                            className="absolute top-2 right-3 bg-black text-white hover:bg-gray-800 rounded-full w-9 h-9 flex items-center justify-center text-lg"
                        >
                            {/* &times; */}
                            <RxCross2 className="" />
                        </button>

                        {/* Heading */}
                        <h2 className="text-lg sm:text-xl font-semibold mb-2">APPOINTMENT CANCELED</h2>

                        {/* Illustration */}
                        <img
                            src={Images.BookingCancel} // replace with your actual image path
                            alt="Appointment Cancelled"
                            className="w-[250px] mx-auto mb-4"
                        />


                        {/* Message */}
                        <p className="text-gray-600 text-sm sm:text-base mb-2">
                            Your appointment has been successfully canceled.
                            <br />
                            A refund of <b>₹4081</b> has been processed to your bank account.
                        </p>

                        <p className="text-gray-500 text-sm sm:text-sm mb-6">
                            If you have any further questions or need to reschedule,
                            <br />
                            please don't hesitate to reach out.
                        </p>

                        {/* Dashboard Button */}
                        <button
                            onClick={closeAll}
                            className="bg-black text-white px-6 py-2 rounded-full hover:bg-gray-800 transition"
                        >
                            Dashboard
                        </button>
                    </div>
                </div>
            )}

        </div>

    );
};

export default Bookings;
