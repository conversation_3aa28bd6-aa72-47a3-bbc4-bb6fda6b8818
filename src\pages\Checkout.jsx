import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

import PaymentMethods from '../components/CheckoutComponents/PaymentMethods';
import Appointment from '../components/CheckoutComponents/Appointment';
import Address from '../components/CheckoutComponents/Address';

const Checkout = () => {
    const [currentStep, setCurrentStep] = useState(1);
    const navigate = useNavigate();

   
    const handleNextStep = () => {
        if (currentStep < 3) {
            setCurrentStep(currentStep + 1);
        } else {
            // Handle checkout completion
            navigate('/confirmation');
        }
    };

    const handlePrevStep = () => {
        if (currentStep > 1) {
            setCurrentStep(currentStep - 1);
        }
    };

   

    return (
        <div className="container sm:mx-auto sm:max-w-[75vw] py-8 px-4">
            {/* Checkout Steps Indicator */}
            <div className="flex justify-between items-center mb-12">
                <div className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep >= 1 ? 'bg-black text-white' : 'bg-gray-200'}`}>
                        1
                    </div>
                    <div className="ml-2">
                        <p className="text-sm font-medium">Step 1</p>
                        <p className="text-[16px] font-semibold">Address</p>
                    </div>
                </div>

                <div className="flex-1 mx-4 border-t border-gray-300"></div>

                <div className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep >= 2 ? 'bg-black text-white' : 'bg-gray-200'}`}>
                        2
                    </div>
                    <div className="ml-2">
                        <p className="text-sm font-medium">Step 2</p>
                        <p className="text-[16px] font-semibold">Schedule An <br className="hidden md:block" /> Appointment</p>
                    </div>
                </div>

                <div className="flex-1 mx-4 border-t border-gray-300"></div>

                <div className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep >= 3 ? 'bg-black text-white' : 'bg-gray-200'}`}>
                        3
                    </div>
                    <div className="ml-2">
                        <p className="text-sm font-medium">Step 3</p>
                        <p className="text-[16px] font-semibold">Payment</p>
                    </div>
                </div>
            </div>

            {/* Step 1: Address Selection */}
            {currentStep === 1 && (
               <Address/>
            )}

            {/* Step 2: Schedule Appointment */}
            {currentStep === 2 && (
                // <div>
                //     Appointment
                // </div>
                <Appointment/>
            )}

            {/* Step 3: Payment */}
            {currentStep === 3 && (
                // <div>
                //     payment
                // </div>
               <PaymentMethods/>
            )}

            {/* Navigation Buttons */}
            <div className="flex justify-end gap-4 mt-12">
                <button
                    onClick={handlePrevStep}
                    className={`px-16 py-3 border border-black text-black rounded-full ${currentStep === 1 ? 'cursor-not-allowed border border-[#F6F6F6]' : 'hover:bg-gray-50 cursor-pointer'}`}
                    disabled={currentStep === 1}
                >
                    Back
                </button>

                <button
                    onClick={currentStep === 3 ? ()=>navigate("/thankyou") :handleNextStep}
                    className="px-16 py-3 bg-black cursor-pointer text-white rounded-full hover:bg-gray-800"
                >
                    {currentStep === 3 ? 'Place Order' : 'Next'}
                </button>
            </div>
        </div>
    );
};

export default Checkout;
