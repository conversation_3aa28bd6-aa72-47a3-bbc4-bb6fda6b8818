import { Routes, Route, Outlet } from "react-router-dom";
import Sidebar from "../components/AccountComponents/Sidebar";
import PersonalInfo from "../components/AccountComponents/PersonalInfo";
import Coupons from "../components/AccountComponents/Coupons";
import ReferFriend from "../components/AccountComponents/ReferFriend";
import Bookings from "../components/AccountComponents/Bookings";

const Account = () => {
  return (
    <div className="flex min-h-screen sm:mt-10">
      <Sidebar />

      <div className="flex-1 p-4">
        <Routes>
          <Route path="/" element={<PersonalInfo />} />
          <Route path="coupons" element={<Coupons />} />
          <Route path="refer" element={<ReferFriend />} />
          <Route path="bookings" element={<Bookings />} />
        </Routes>
      </div>
    </div>
  );
};

export default Account;
