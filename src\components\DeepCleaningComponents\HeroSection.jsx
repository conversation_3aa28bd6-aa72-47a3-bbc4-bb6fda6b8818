import React, { useState } from 'react'
import ServiceCard from '../GlobalComponents/ServiceCard'
import Images from '../../assets/images'
import { Link } from 'react-router-dom'
import Steps from './Steps'

const HeroSection = () => {
    const [showSteps, setShowSteps] = useState(false);
    const [selectedService, setSelectedService] = useState(null);

    const handleServiceClick = (service) => {
        setSelectedService(service);
        setShowSteps(true);
    };

    return (
        <section className='flex flex-col gap-8 px-4 md:px-8 lg:px-8 w-[90vw]'>
            {/* Show Steps modal when showSteps is true */}
            {showSteps && (
                <Steps 
                    onClose={() => setShowSteps(false)} 
                    service={selectedService} 
                />
            )}

            {/* Breadcrumb Navigation */}
            <div className='flex flex-col lg:flex-row lg:gap-10 items-start'>

                {/* Left Content */}
                <div className='flex flex-col gap-18 my-6 lg:w-[60%]'>
                    <div className="flex items-center gap-2 text-sm text-gray-600 bg-white shadow-2xl px-6 py-2 w-fit rounded-full">
                        <Link to="/">Home</Link>
                        <span>›</span>
                        <Link to="/services">Services</Link>
                        <span>›</span>
                        <Link to="/deepcleaning" className="font-medium">Deep Home Cleaning</Link>
                    </div>
                    {/* Service Selection */}
                    <div>
                        <p className="text-gray-600 mb-4">Select a Service</p>
                        <div className="grid grid-cols-2 gap-4 mt-8 sm:w-[90%]">
                            <div onClick={() => handleServiceClick('Deep Cleaning')}>
                                <ServiceCard img={Images.Cleaning} text="Deep Cleaning" />
                            </div>
                            <div onClick={() => handleServiceClick('Comprehensive Cleaning')}>
                                <ServiceCard img={Images.Plumbing} text="Comprehensive Cleaning" />
                            </div>
                            <div onClick={() => handleServiceClick('Regular Cleaning')}>
                                <ServiceCard img={Images.Electrical} text="Regular Cleaning" />
                            </div>
                        </div>
                    </div>
                </div>

                {/* Right Content - Custom Image Grid */}
                <div className="lg:w-[40%] h-[550px] relative overflow-hidden ">
                    {/* Left Column */}
                    <div className="absolute top-[-120px] left-0 w-[30%] flex flex-col gap-6">
                        <img
                            src="https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60"
                            alt="Cleaning"
                            className="rounded-full object-cover h-[260px] w-full"
                        />
                        <img
                            src="https://images.unsplash.com/photo-1584622650111-993a426fbf0a?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60"
                            alt="Cleaning"
                            className="rounded-full object-cover h-[260px] w-full"
                        />
                        <img
                            src="https://images.unsplash.com/photo-1556911220-bff31c812dba?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60"
                            alt="Cleaning"
                            className="rounded-full object-cover h-[260px] w-full"
                        />
                    </div>

                    {/* Middle Column */}
                    <div className="absolute top-[-50px] left-[35%] w-[30%] flex flex-col gap-6">
                        <img
                            src="https://images.unsplash.com/photo-1600566753086-00f18fb6b3ea?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60"
                            alt="Cleaning"
                            className="rounded-full object-cover h-[400px] w-full"
                        />
                        <img
                            src="https://images.unsplash.com/photo-1563453392212-326f5e854473?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60"
                            alt="Cleaning"
                            className="rounded-full object-cover h-[400px] w-full"
                        />
                    </div>

                    {/* Right Column */}
                    <div className="absolute top-[-180px] right-0 w-[30%] flex flex-col gap-6">
                        <img
                            src="https://images.unsplash.com/photo-1527515637462-cff94eecc1ac?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60"
                            alt="Cleaning"
                            className="rounded-full object-cover h-[400px] w-full"
                        />
                        <img
                            src="https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60"
                            alt="Cleaning"
                            className="rounded-full object-cover h-[400px] w-full"
                        />
                    </div>
                </div>

            </div>

            {/* Announcement Banner */}
            <div className="rounded-lg p-4 flex items-center justify-start text-red-600 mt-4">
                <span className="mr-2">🔊</span>
                <p className="text-sm font-medium">
                    Great news! 10 services booked this week. Book now for top-notch care!
                </p>
            </div>
        </section>
    )
}

export default HeroSection