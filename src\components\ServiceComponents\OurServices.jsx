import React from 'react';
import Heading from '../GlobalComponents/Heading';
import Images from '../../assets/images';

const services = [
    {
      id: 1,
      title: 'Deep Home Cleaning',
      description: 'The Deep Cleaning services are solely performed by professionals who would like correct instrumentality.',
      price: '₹3500',
      image: "https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60"
    },
    {
      id: 2,
      title: 'Sofa/Carpet Cleaning',
      description: 'We provide you the best deep Sofa and Carpet cleaning services which is fully mechanized, environment safe.',
      price: '₹3500',
      image: "https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60"
    },
    {
      id: 3,
      title: 'Kitchen Cleaning',
      description: 'Our kitchen cleaning service leaves every surface spotless, sanitized, sparkling and ready to use!',
      price: '₹3500',
      image: "https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60"
    },
    {
      id: 4,
      title: 'Bathroom Cleaning',
      description: 'We provide thorough bathroom cleaning which ensures clean toilet seats, shower tiles, and floor.',
      price: '₹3500',
      image: "https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60"
    },
    {
      id: 5,
      title: 'Water Tank Cleaning',
      description: 'We provide professional cleaning for your water tank, water filter cleaning / sanitization with high-tech equipment.',
      price: '₹3500',
      image: "https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60"
    },
    {
      id: 6,
      title: 'Home Maintenance',
      description: 'Our home maintenance services are designed to keep your space functional & well-maintained with everything working as it should.',
      price: '₹3500',
      image: "https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60"
    }
  ];
  
const OurServices = () => {
  return (
    <section className="py-2 px-4 md:px-10 lg:px-28 w-[100vw]">
      <div className="">
        {/* Heading Section */}
        <Heading
          Heading="Our Services"
          Description="Popular services include deep cleaning, regular upkeep, move-in/move-out, and post-renovation cleaning for a spotless home."
        />
        
        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12">
          {services.map((service) => (
            <div key={service.id} className="bg-white rounded-lg shadow-xl overflow-hidden p-6 relative">
              {/* Service Title */}
              <h3 className="text-xl font-semibold mb-4">{service.title}</h3>
              
              {/* Service Image */}
              <div className="h-48 overflow-hidden rounded-lg mb-4">
                <img src={service.image} alt={service.title} className="w-full h-full object-cover" />
              </div>
              
              {/* Service Description */}
              <p className="text-gray-600 text-sm mb-4">{service.description}</p>
              
              {/* Service Price */}
              <div className="font-bold text-xl mb-4">{service.price}</div>
              
              {/* Service Features */}
              <div className="grid grid-cols-2 gap-2 mb-6">
                <div className="flex items-center gap-2 border border-gray-400 px-2 py-1 rounded-lg">
                  <img src={Images.Cleaning || "/images/deep-cleaning-icon.png"} alt="Deep Cleaning" className="w-8 h-8" />
                  <span className="text-xs font-semibold">Deep Cleaning</span>
                </div>
                <div className="flex items-center gap-2 border border-gray-400 px-2 py-1 rounded-lg">
                  <img src={Images.Cleaning || "/images/comprehensive-cleaning-icon.png"} alt="Comprehensive Cleaning" className="w-8 h-8" />
                  <span className="text-xs font-semibold">Comprehensive Cleaning</span>
                </div>
                <div className="flex items-center gap-2 border border-gray-400 px-2 py-1 rounded-lg">
                  <img src={Images.Cleaning || "/images/regular-cleaning-icon.png"} alt="Regular Cleaning" className="w-8 h-8" />
                  <span className="text-xs font-semibold">Regular Cleaning</span>
                </div>
              </div>
              
              {/* View More Button */}
              <div className='flex justify-end w-full'>

              <button className=" bg-black w-auto text-white text-sm py-2 px-4 rounded-full hover:bg-gray-800 transition-colors ">
                View More
              </button>
              </div>
              
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default OurServices
