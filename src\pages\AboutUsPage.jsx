import { styled } from 'styled-components';
import { Fi<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>get, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-icons/fi';
import HeroSection from '../components/AboutUsComponents/HeroSection';
import Description from '../components/AboutUsComponents/Description';
import TeamMembers from '../components/AboutUsComponents/TeamMembers';
import Covid from '../components/AboutUsComponents/Covid';
import HappyClients from '../components/HomeComponents/HappyClients';



function AboutUsPage() {
  // const values = [
  //   {
  //     icon: <FiUsers size={24} />,
  //     title: 'Customer First',
  //     description: 'We prioritize our customers needs and satisfaction above all else.'
  //   },
  //   {
  //     icon: <FiTarget size={24} />,
  //     title: 'Excellence',
  //     description: 'We strive for excellence in every service we provide.'
  //   },
  //   {
  //     icon: <FiHeart size={24} />,
  //     title: 'Integrity',
  //     description: 'We conduct our business with honesty, transparency, and ethical standards.'
  //   },
  //   { 
  //     icon: <FiAward size={24} />,
  //     title: 'Reliability',
  //     description: 'We are committed to being dependable and consistent in our service delivery.'
  //   }
  // ];

  return (
    <div className='flex flex-col items-center gap-24 py-8 justify-center'>
     <HeroSection/>
    {/*  <Description/>
     <TeamMembers/>
     <Covid/>
     <HappyClients/> */}
    </div>
  );
}

export default AboutUsPage;